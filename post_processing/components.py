import requests
import json
import time
from typing import Dict, Any, Optional


def fulfill_component(node_info: dict, api_url: str = "https://app-dev.rapidinnovation.dev/api/v1/components") -> Dict[str, Any]:
    """
    Generate a complete workflow node structure for the given node name.
    
    Args:
        node_name (str): Name of the component (e.g., "SelectDataComponent")
        api_url (str): API endpoint URL
    
    Returns:
        dict: Complete node structure with definition from API
    """
    try:
        # Fetch component definitions from API
        # response = requests.get(api_url)
        # response.raise_for_status()
        
        # components_data = response.json()
        with open("post_processing/data retrival/components.json", "r") as f:
            components_data = json.load(f)
        # Find the component definition by searching through all categories
        component_def = None
        node_name = node_info["OriginalType"]
        for category, components in components_data.items():
            if node_name in components:
                component_def = components[node_name]
                break
        
        if not component_def:
            # List available components for user reference
            available_components = []
            for category, components in components_data.items():
                available_components.extend(components.keys())
            
            error_msg = f"Component '{node_name}' not found in API response.\n"
            error_msg += f"Available components ({len(available_components)}):\n"
            error_msg += "\n".join([f"  - {comp}" for comp in sorted(available_components)[:20]])
            if len(available_components) > 20:
                error_msg += f"\n  ... and {len(available_components) - 20} more"
            
            return {"error": error_msg}
        
        # Generate unique ID with timestamp
        unique_id = node_info["node_id"]
        
        # Create the complete node structure
        node_structure = {
            "id": unique_id,
            "type": "WorkflowNode",
            "position": {"x": 1760, "y": 1960},
            "data": {
                "label": component_def.get("display_name", node_name),
                "type": "component", ## condition could be wrong, need to do TODO
                "originalType": node_name,
                "definition": component_def,  # This contains all the API data
                "config": {}
            },
            "width": 208,
            "height": 194,
            "selected": False,
            "dragging": False,
            "style": {"opacity": 1}
        }
        
        node_structure["position"] = node_info["position"]
        node_structure["data"]["label"] = node_info["label"]
        node_structure["data"]["config"] = node_info["parameters"]
        node_structure["width"] = node_info["dimension"]["width"]
        node_structure["height"] = node_info["dimension"]["height"]
        return node_structure
        
    except requests.exceptions.RequestException as e:
        return {"error": f"API request failed: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"Failed to parse API response: {str(e)}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}