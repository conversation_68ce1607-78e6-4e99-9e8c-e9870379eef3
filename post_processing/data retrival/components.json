{"AI": {"AgenticAI": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}}, "Logic": {"ConditionalNode": {"name": "ConditionalNode", "display_name": "Switch-Case Router", "description": "Evaluates multiple conditions and routes data to matching outputs", "category": "Logic", "icon": "GitBranch", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "Input data that will be routed when conditions match. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "source", "display_name": "Data Source", "info": "Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a direct global context value.", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "global_context_value", "display_name": "Global Context Value", "info": "The actual value from global context to evaluate conditions against (e.g., 'premium', 'basic', 'admin'). This value will be compared directly against each condition's expected value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "condition_1_operator", "display_name": "Condition 1 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_1_expected_value", "display_name": "Condition 1 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_1_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_conditions", "display_name": "Number of Additional Conditions", "info": "Number of additional conditions beyond the base 1 condition (0-9).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "evaluation_strategy", "display_name": "Evaluation Strategy", "info": "Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "all_matches", "options": ["first_match", "all_matches"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_operator", "display_name": "Condition 2 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_expected_value", "display_name": "Condition 2 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "condition_2_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_2_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_global_context_value", "display_name": "Condition 2 - Global Context Value", "info": "The specific global context value to use for condition 2 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_operator", "display_name": "Condition 3 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_expected_value", "display_name": "Condition 3 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "condition_3_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_3_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_global_context_value", "display_name": "Condition 3 - Global Context Value", "info": "The specific global context value to use for condition 3 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_operator", "display_name": "Condition 4 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_expected_value", "display_name": "Condition 4 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "condition_4_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_4_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_global_context_value", "display_name": "Condition 4 - Global Context Value", "info": "The specific global context value to use for condition 4 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_operator", "display_name": "Condition 5 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_expected_value", "display_name": "Condition 5 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "condition_5_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_5_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_global_context_value", "display_name": "Condition 5 - Global Context Value", "info": "The specific global context value to use for condition 5 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_operator", "display_name": "Condition 6 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_expected_value", "display_name": "Condition 6 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "condition_6_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_6_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_global_context_value", "display_name": "Condition 6 - Global Context Value", "info": "The specific global context value to use for condition 6 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_operator", "display_name": "Condition 7 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_expected_value", "display_name": "Condition 7 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "condition_7_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_7_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_global_context_value", "display_name": "Condition 7 - Global Context Value", "info": "The specific global context value to use for condition 7 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_operator", "display_name": "Condition 8 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_expected_value", "display_name": "Condition 8 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "condition_8_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_8_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_global_context_value", "display_name": "Condition 8 - Global Context Value", "info": "The specific global context value to use for condition 8 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_operator", "display_name": "Condition 9 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_expected_value", "display_name": "Condition 9 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "condition_9_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_9_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_global_context_value", "display_name": "Condition 9 - Global Context Value", "info": "The specific global context value to use for condition 9 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_operator", "display_name": "Condition 10 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_expected_value", "display_name": "Condition 10 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "condition_10_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_10_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_global_context_value", "display_name": "Condition 10 - Global Context Value", "info": "The specific global context value to use for condition 10 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "default", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.conditionalnode", "interface_issues": []}, "LoopNode": {"name": "LoopNode", "display_name": "For Each Loop", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "category": "Logic", "icon": "Repeat", "beta": false, "requires_approval": false, "inputs": [{"name": "source_type", "display_name": "Iteration Source", "info": "Choose whether to iterate over a list of items or a number range.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "iteration_list", "options": ["iteration_list", "number_range"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_list", "display_name": "Iteration List", "info": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.", "input_type": "list", "input_types": ["array", "list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "batch_size", "display_name": "<PERSON><PERSON> Si<PERSON>", "info": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "start", "display_name": "Start Number", "info": "Starting number for the range. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "end", "display_name": "End Number", "info": "Ending number for the range (inclusive). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "10", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "step", "display_name": "Step Size", "info": "Step size for the range (default: 1). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "parallel_execution", "display_name": "Parallel Execution", "info": "Execute loop iterations in parallel for better performance.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_concurrent", "display_name": "Max Concurrent Iterations", "info": "Maximum number of iterations to run concurrently (1-20).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": [{"field_name": "parallel_execution", "field_value": "True", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "preserve_order", "display_name": "Preserve Order", "info": "Maintain the original order of items in the results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_timeout", "display_name": "Iteration Timeout (seconds)", "info": "Maximum time to wait for each iteration to complete (1-3600 seconds).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 60, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "aggregation_type", "display_name": "Result Aggregation", "info": "How to aggregate results from all iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "collect_all", "options": ["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text", "return_original"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_metadata", "display_name": "Include Metadata", "info": "Include metadata (timing, iteration index, etc.) in results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_iteration_error", "display_name": "On Iteration Error", "info": "How to handle errors in individual iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "continue", "options": ["continue", "retry_once", "retry_twice", "exit_loop"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_errors", "display_name": "Include Errors in Results", "info": "Include error information in the final results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "current_item", "display_name": "Current Item (Iteration Output)", "output_type": "object", "semantic_type": null, "method": null}, {"name": "final_results", "display_name": "All Results (Exit Output)", "output_type": "array", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.loopnode", "interface_issues": []}}, "Data Interaction": {"ApiRequestNode": {"name": "ApiRequestNode", "display_name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "category": "Data Interaction", "icon": "Globe", "beta": false, "requires_approval": false, "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "requirement_logic": "OR"}], "outputs": [{"name": "data", "display_name": "Response Data/Body", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "semantic_type": null, "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.data interaction.apirequestnode", "interface_issues": []}}, "Helpers": {"IDGeneratorComponent": {"name": "IDGeneratorComponent", "display_name": "ID Generator", "description": "Generates various types of unique identifiers (UUID, timestamp, short ID).", "category": "Helpers", "icon": "Fingerprint", "beta": false, "requires_approval": false, "inputs": [{"name": "trigger", "display_name": "<PERSON><PERSON>", "info": "Optional input to control when ID generation occurs. Leave unconnected for immediate execution.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "id_type", "display_name": "ID Type", "info": "The type of unique identifier to generate.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "UUIDv4", "options": ["UUIDv4", "Timestamp ID", "Short ID"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "short_id_length", "display_name": "Short ID Length", "info": "The length of the short ID (only used when ID Type is 'Short ID').", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 8, "options": null, "visibility_rules": [{"field_name": "id_type", "field_value": "Short ID", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "unique_id", "display_name": "Unique ID", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.helpers.idgeneratorcomponent", "interface_issues": []}}, "IO": {"StartNode": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}}, "Processing": {"AlterMetadataComponent": {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "description": "Modifies metadata dictionary keys.", "category": "Processing", "icon": "Tag", "beta": false, "requires_approval": false, "inputs": [{"name": "input_metadata", "display_name": "Input Metadata", "info": "The metadata dictionary to modify. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "updates", "display_name": "<PERSON>ada<PERSON> Updates", "info": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "keys_to_remove", "display_name": "Keys to Remove", "info": "List of keys to remove from the metadata. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_metadata", "display_name": "Updated Metadata", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.altermetadatacomponent", "interface_issues": []}, "CombineTextComponent": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "DataToDataFrameComponent": {"name": "DataToDataFrameComponent", "display_name": "Data to DataFrame", "description": "Converts data to a Pandas DataFrame.", "category": "Processing", "icon": "Table", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to convert to a DataFrame. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "orientation", "display_name": "Data Orientation", "info": "The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "auto-detect", "options": ["records", "columns", "auto-detect"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_dataframe", "display_name": "DataFrame", "output_type": "DataFrame", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.datatodataframecomponent", "interface_issues": []}, "DelayComponent": {"name": "DelayComponent", "display_name": "Wait / Delay", "description": "Pauses the workflow execution for a set number of seconds.", "category": "Processing", "icon": "Timer", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The input data to be passed through.", "input_type": "string", "input_types": ["number", "string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delay_seconds", "display_name": "Delay (seconds)", "info": "The number of seconds to pause the workflow.", "input_type": "string", "input_types": ["number", "string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "30", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output", "display_name": "Output", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "Message", "display_name": "Message", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.delaycomponent", "interface_issues": []}, "MergeDataComponent": {"name": "MergeDataComponent", "display_name": "Merge Data", "description": "Combines multiple dictionaries or lists.", "category": "Processing", "icon": "Combine", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main data structure to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "merge_strategy", "display_name": "Merge Strategy (Dicts)", "info": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Overwrite", "options": ["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_1", "display_name": "Output Key 1", "info": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_2", "display_name": "Output Key 2", "info": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_3", "display_name": "Output Key 3", "info": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_4", "display_name": "Output Key 4", "info": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_5", "display_name": "Output Key 5", "info": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_6", "display_name": "Output Key 6", "info": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_7", "display_name": "Output Key 7", "info": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_8", "display_name": "Output Key 8", "info": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_9", "display_name": "Output Key 9", "info": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_10", "display_name": "Output Key 10", "info": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_11", "display_name": "Output Key 11", "info": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Data structure 1 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Data structure 2 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Data structure 3 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Data structure 4 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Data structure 5 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Data structure 6 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Data structure 7 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Data structure 8 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Data structure 9 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Data structure 10 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Merged Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.mergedatacomponent", "interface_issues": []}, "MessageToDataComponent": {"name": "MessageToDataComponent", "display_name": "Message To Data", "description": "Extracts fields from a Message object.", "category": "Processing", "icon": "Package", "beta": false, "requires_approval": false, "inputs": [{"name": "input_message", "display_name": "Input Message", "info": "The Message object to extract fields from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["Message", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "fields_to_extract", "display_name": "Fields to Extract", "info": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output", "display_name": "Extracted Data", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.messagetodatacomponent", "interface_issues": []}, "RegexExtractorComponent": {"name": "RegexExtractorComponent", "display_name": "Regex Extractor", "description": "Extract data from text using regular expressions", "category": "Processing", "icon": "Search", "beta": false, "requires_approval": false, "inputs": [{"name": "input_text", "display_name": "Input Text", "info": "The source text content to be searched. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "regex_pattern", "display_name": "Regex Pattern", "info": "Regular expression pattern to match. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "extraction_mode", "display_name": "Extraction Mode", "info": "Find first match only or all matches", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "first_match", "options": ["first_match", "all_matches"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_format", "display_name": "Output Format", "info": "Format of extracted data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "full_match", "options": ["full_match", "first_capture_group", "named_groups"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "case_insensitive", "display_name": "Case Insensitive (i)", "info": "Ignore case when matching", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "multiline", "display_name": "Multiline (m)", "info": "^ and $ match start/end of lines", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "dot_all", "display_name": "Dot All (s)", "info": ". matches newlines", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_no_match", "display_name": "On No Match", "info": "Behavior when no match is found", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "continue_empty", "options": ["continue_empty", "fail_workflow"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "matches", "display_name": "Matches", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "first_match", "display_name": "First Match", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "match_count", "display_name": "Match Count", "output_type": "int", "semantic_type": null, "method": null}, {"name": "is_match_found", "display_name": "Is Match Found", "output_type": "bool", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.regexextractorcomponent", "interface_issues": []}, "SelectDataComponent": {"name": "SelectDataComponent", "display_name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "category": "Processing", "icon": "Filter", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "search_mode", "display_name": "Search Mode", "info": "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Smart Search", "options": ["Exact Path", "Smart Search"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "field_matching_mode", "display_name": "Field Matching Mode", "info": "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-detect", "options": ["Auto-detect", "Key-based Only", "Property-based Only"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "selector", "display_name": "Selector", "info": "Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.selectdatacomponent", "interface_issues": []}, "SplitTextComponent": {"name": "SplitTextComponent", "display_name": "Split Text", "description": "Splits text into a list using a delimiter.", "category": "Processing", "icon": "Scissors", "beta": false, "requires_approval": false, "inputs": [{"name": "input_text", "display_name": "Input Text", "info": "The text to split. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delimiter", "display_name": "Delimiter", "info": "The character or string to split the text by.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ",", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_splits", "display_name": "Max Splits", "info": "Maximum number of splits to perform. -1 means no limit.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": -1, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_delimiter", "display_name": "Include Delimiter", "info": "If enabled, the delimiter will be included at the end of each split part (except the last one).", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_list", "display_name": "Split List", "output_type": "list", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.splittextcomponent", "interface_issues": []}, "UniversalConverterComponent": {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "description": "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)", "category": "Processing", "icon": "ArrowRightLeft", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to convert. Can be any type - string, object, array, number, etc. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "from_type", "display_name": "From Type", "info": "The current type of your input data. Auto-detect will determine this automatically.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-detect", "options": ["Auto-detect", "String", "Number", "Boolean", "Object", "Array", "<PERSON><PERSON>"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "to_type", "display_name": "To Type", "info": "The target type to convert to. JSON String: pretty-formatted JSON. CSV String: comma-separated values. Joined String: array elements joined with delimiter. Split Array: string split by delimiter. Flattened Object: nested object flattened to dot notation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "String", "options": ["String", "Number", "Boolean", "Object", "Array", "JSON String", "CSV String", "Joined String", "Split Array", "Flattened Object"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delimiter", "display_name": "Delimiter", "info": "Delimiter for CSV/Join/Split operations (e.g., ',', ';', '|', ' ', '\\t' for tab, '\\n' for newline)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ",", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "pretty_format", "display_name": "Pretty Format", "info": "For JSON String output, use pretty formatting with indentation and line breaks", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "converted_data", "display_name": "Converted Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "original_type", "display_name": "Original Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "target_type", "display_name": "Target Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.universalconvertercomponent", "interface_issues": []}}}