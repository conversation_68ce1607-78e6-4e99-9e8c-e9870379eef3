{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "name": "content-extractor-mcp", "logo": null, "description": "generate subtitle and scrape the data", "category": "general", "tags": ["automation", "subtitle", "scrape"], "created_at": "2025-06-15T10:43:21.042544", "updated_at": "2025-08-04T05:20:07.691968", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://content-extractor-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["scrape_web", "scrape_web_using_browser_use", "scrape_web_using_fire_crawler", "generate_subtitle"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "scrape_web", "description": "Extract content from a web page or article link.", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}}, "required": ["link"], "title": "ScrapeWeb", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "scrape_web_using_browser_use", "description": "Use a browser-based approach to scrape data from a web page.", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}}, "required": ["link"], "title": "ScrapeWeb", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "scrape_web_using_fire_crawler", "description": "Utilize the FireCrawl tool to scrape data from a web page.", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}, "raw_data": {"description": "yes or no", "title": "Raw Data", "type": "boolean"}}, "required": ["link", "raw_data"], "title": "FireCrawlScrapeWeb", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "generate_subtitle", "description": "Generate subtitles for a video by processing its audio.", "input_schema": {"properties": {"audio_urls": {"description": "List of audio URLs is required", "items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["audio_urls", "script"], "title": "GenerateSubtitle", "type": "object"}, "output_schema": {"properties": {"subtitle": {"type": "string", "description": "generated subtitle of the audio", "title": "subtitle"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}