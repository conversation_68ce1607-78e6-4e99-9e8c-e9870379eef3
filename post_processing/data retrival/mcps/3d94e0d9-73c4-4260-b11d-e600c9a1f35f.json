{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "name": "Zoho CRM", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/zoho.png/**********-zoho.png", "description": "Zoho CRM MCP Server", "category": "sales", "tags": null, "created_at": "2025-07-04T10:30:52.885652", "updated_at": "2025-08-20T12:09:33.777847", "owner_id": "985d0f11-2fb1-4f16-bfa5-babb0689bbfa", "hosted_url": "https://zoho-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["create_record", "health_check", "get_tool_info", "search_records", "get_records"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_record", "description": "Create a new record in Zoho CRM module.\n\nRequired fields by module:\n• Leads: Last_Name, Company\n• Contacts: Last_Name  \n• Accounts: Account_Name\n• Deals: Deal_Name, Stage, Closing_Date\n\nCommon optional fields:\n• Email, Phone, Mobile, Website, Description\n• Lead_Source (for Leads), Industry, Annual_Revenue\n\nExample: Create a lead with Last_Name=\"Smith\", Company=\"Acme Corp\", Email=\"<EMAIL>\"\n", "input_schema": {"type": "object", "properties": {"module": {"type": "string", "description": "CRM module: 'Leads', 'Contacts', 'Accounts', 'Deals', 'Tasks', 'Events', 'Calls'", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"]}, "record_data": {"type": "object", "description": "Record data with field names and values", "additionalProperties": true}}, "required": ["module", "record_data"]}, "output_schema": {"properties": {"hjbbhb": {"type": "string", "description": "jbbjhb", "title": "hjbbhb"}}}, "annotations": null}, {"name": "health_check", "description": "Check server health and configuration status.\n\nReturns:\n• Server status and uptime\n• Zoho API connectivity information\n• Configuration details\n• Authentication status\n\nUse this tool to verify the server is running correctly and can connect to Zoho APIs.\n", "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": {"properties": {"jvvj": {"type": "string", "description": "jhvvhvjhv", "title": "jvvj"}}}, "annotations": null}, {"name": "get_tool_info", "description": "Get detailed information about available tools and their parameters.\n\nFeatures:\n• Complete parameter specifications for each tool\n• Required vs optional field information  \n• Module-specific field requirements\n• Usage examples and best practices\n• Search operator documentation\n\nUse without tool_name to get info for all tools, or specify tool_name for specific tool details.\n", "input_schema": {"type": "object", "properties": {"tool_name": {"type": "string", "description": "Specific tool name (if not provided, returns info for all tools)", "enum": ["create_record", "get_records", "search_records", "health_check"]}}, "required": []}, "output_schema": {"properties": {"hjbjh": {"type": "string", "description": "hjbhbhj", "title": "hjbjh"}}}, "annotations": null}, {"name": "search_records", "description": "Search records using advanced criteria with multiple operators.\n\nSearch operators:\n• equals: Exact match\n• contains: Partial match (case-insensitive)  \n• starts_with: Begins with value\n• ends_with: Ends with value\n• less_than, greater_than: Numeric/date comparison\n\nCriteria format:\n• Single: (Field_Name:operator:value)\n• Multiple: ((Field1:equals:value1)and(Field2:contains:value2))\n• Date: (Created_Time:greater_than:2024-01-01T00:00:00Z)\n\nExamples:\n• Find leads by company: (Company:contains:Tech)\n• Multiple criteria: ((Last_Name:equals:<PERSON>)and(Lead_Source:equals:Website))\n• Recent records: (Created_Time:greater_than:2024-01-01T00:00:00Z)\n", "input_schema": {"type": "object", "properties": {"module": {"type": "string", "description": "CRM module: 'Leads', 'Contacts', 'Accounts', 'Deals', 'Tasks', 'Events', 'Calls'", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"]}, "search_criteria": {"type": "string", "description": "Search criteria in Zoho format: (Field:operator:value)"}, "page": {"type": "integer", "description": "Page number (default: 1, min: 1)", "minimum": 1, "default": 1}, "per_page": {"type": "integer", "description": "Records per page (default: 200, max: 200)", "minimum": 1, "maximum": 200, "default": 200}}, "required": ["module", "search_criteria"]}, "output_schema": {"properties": {"ghvv": {"type": "string", "description": "hnhvh", "title": "ghvv"}}}, "annotations": null}, {"name": "get_records", "description": "Retrieve records from Zoho CRM with pagination and field selection.\n\nFeatures:\n• Pagination support (page, per_page)\n• Field selection to optimize performance\n• Automatic handling of large datasets\n\nCommon fields by module:\n• Leads: Last_Name, Company, Email, Phone, Lead_Source, Created_Time\n• Contacts: Last_Name, First_Name, Email, Phone, Account_Name\n• Accounts: Account_Name, Website, Industry, Annual_Revenue\n• Deals: Deal_Name, Amount, Stage, Closing_Date, Account_Name\n\nExample: Get first 50 leads with specific fields\n", "input_schema": {"type": "object", "properties": {"module": {"type": "string", "description": "CRM module: 'Leads', 'Contacts', 'Accounts', 'Deals', 'Tasks', 'Events', 'Calls'", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"]}, "fields": {"type": "array", "items": {"type": "string"}, "description": "Specific fields to retrieve (if not provided, returns all fields)"}, "page": {"type": "integer", "description": "Page number (default: 1, min: 1)", "minimum": 1, "default": 1}, "per_page": {"type": "integer", "description": "Records per page (default: 50, max: 200)", "minimum": 1, "maximum": 200, "default": 50}}, "required": ["module"]}, "output_schema": {"properties": {"bjhhj": {"type": "string", "description": "hjbhbj", "title": "bjhhj"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["26c77e5b-fdb9-42a9-9c85-8d96c687d2f0"], "url": null}}