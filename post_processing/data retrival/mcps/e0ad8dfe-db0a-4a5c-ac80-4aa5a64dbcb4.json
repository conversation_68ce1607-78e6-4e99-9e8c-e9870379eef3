{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "name": "Bible MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/81Me58A46fL._UF1000%2C1000_QL80_.jpg/1752583131-81Me58A46fL._UF10001000_QL80_.jpg", "description": "Get Bible related information", "category": "general", "tags": ["Bible", "biblical"], "created_at": "2025-07-15T12:39:17.852884", "updated_at": "2025-08-15T08:18:33.785572", "owner_id": "9761e951-93d9-41b1-a25a-fa1ae2ac6d70", "hosted_url": "https://bible-mcp-dev-624209391722.us-central1.run.app/mcp/", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://gitlab.rapidinnovation.tech/mcp-server/bible-mcp/-/tree/dev?ref_type=heads", "api_documentation": null, "capabilities": ["get_random_bible_verse", "get_bible_verse", "list_bible_translations", "list_bible_books", "list_bible_chapters"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "get_random_bible_verse", "description": "\n    Get a random Bible verse.\n\n    Args:\n        book_ids: Optional comma-separated list of book IDs (e.g., \"GEN,JHN\")\n                 or special strings \"OT\" (Old Testament) or \"NT\" (New Testament)\n\n    Returns:\n        Random verse data including reference, text, and translation info\n    ", "input_schema": {"properties": {"book_ids": {"default": null, "title": "Book Ids", "type": "string"}}, "title": "get_random_bible_verseArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_bible_verse", "description": "\n    Get a specific Bible verse or passage.\n\n    Args:\n        reference: Bible reference (e.g., \"John 3:16\", \"Genesis 1:1-3\")\n        translation: Translation identifier (default: \"web\" for World English Bible)\n\n    Returns:\n        Verse data including reference, text, and translation info\n    ", "input_schema": {"properties": {"reference": {"title": "Reference", "type": "string"}, "translation": {"default": "web", "title": "Translation", "type": "string"}}, "required": ["reference"], "title": "get_bible_verseArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_bible_translations", "description": "\n    Get list of all available Bible translations.\n\n    Returns:\n        List of available translations with their identifiers and names\n    ", "input_schema": {"properties": {}, "title": "list_bible_translationsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_bible_books", "description": "\n    Get list of books for a specific Bible translation.\n\n    Args:\n        translation: Translation identifier (default: \"web\")\n\n    Returns:\n        List of books with their identifiers and names\n    ", "input_schema": {"properties": {"translation": {"default": "web", "title": "Translation", "type": "string"}}, "title": "list_bible_booksArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_bible_chapters", "description": "\n    Get chapters for a specific Bible book.\n\n    Args:\n        book: Book identifier (e.g., \"JHN\" for <PERSON>, \"GEN\" for Genesis)\n        translation: Translation identifier (default: \"web\")\n\n    Returns:\n        List of chapters for the specified book\n    ", "input_schema": {"properties": {"book": {"title": "Book", "type": "string"}, "translation": {"default": "web", "title": "Translation", "type": "string"}}, "required": ["book"], "title": "list_bible_chaptersArguments", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "bible-mcp", "git_user_name": "mcp-server", "integrations": null, "url": null}}