{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "a4dbc53f-af77-416f-9849-c82f0411695b", "name": "Webflow", "logo": null, "description": "Webflow Server", "category": "general", "tags": ["webflow", "blogs", "blog publisher"], "created_at": "2025-06-26T13:28:29.755749", "updated_at": "2025-07-10T07:53:15.635198", "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "hosted_url": "https://server.smithery.ai/@webflow/mcp-server/mcp?profile=mechanical-snail-g1RifI&api_key=2bf9a5dd-cc32-424f-a0c3-cdc9031c3799", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["collection_fields_create_option", "collection_fields_create_reference", "collection_fields_update", "collections_items_create_item_live", "collections_items_update_items_live", "collections_items_list_items", "collections_items_create_item", "collections_items_update_items", "collections_items_publish_items", "sites_list", "sites_get", "sites_publish", "pages_list", "pages_get_metadata", "pages_update_page_settings", "pages_get_content", "pages_update_static_content", "collections_list", "collections_get", "collections_create", "collection_fields_create_static"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "collection_fields_create_option", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "const": "Option"}, "displayName": {"type": "string"}, "helpText": {"type": "string"}, "metadata": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}}, "required": ["options"], "additionalProperties": false}}, "required": ["type", "displayName", "metadata"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_create_reference", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "enum": ["MultiReference", "Reference"]}, "displayName": {"type": "string"}, "helpText": {"type": "string"}, "metadata": {"type": "object", "properties": {"collectionId": {"type": "string"}}, "required": ["collectionId"], "additionalProperties": false}}, "required": ["type", "displayName", "metadata"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_update", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "field_id": {"type": "string"}, "request": {"type": "object", "properties": {"isRequired": {"type": "boolean"}, "displayName": {"type": "string"}, "helpText": {"type": "string"}}, "additionalProperties": false}}, "required": ["collection_id", "field_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_create_item_live", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_update_items_live", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}}]}}, "required": ["id"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_list_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "offset": {"type": "number"}, "limit": {"type": "number"}, "name": {"type": "string"}, "slug": {"type": "string"}, "sortBy": {"type": "string", "enum": ["lastPublished", "name", "slug"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}, "required": ["collection_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_create_item", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_update_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "cmsLocaleId": {"type": "string"}, "lastPublished": {"type": "string"}, "lastUpdated": {"type": "string"}, "createdOn": {"type": "string"}, "isArchived": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "fieldData": {"allOf": [{"type": "object", "additionalProperties": {}}, {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["name", "slug"]}]}}, "required": ["id", "fieldData"], "additionalProperties": false}}}, "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_items_publish_items", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "itemIds": {"type": "array", "items": {"type": "string"}}}, "required": ["collection_id", "itemIds"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "sites_list", "description": null, "input_schema": {"type": "object"}, "output_schema": null, "annotations": null}, {"name": "sites_get", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"sites": {"type": "array", "description": "List of Webflow sites", "title": "sites"}}}, "annotations": null}, {"name": "sites_publish", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "customDomains": {"type": "array", "items": {"type": "string"}}, "publishToWebflowSubdomain": {"type": "boolean", "default": false}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_list", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "localeId": {"type": "string"}, "limit": {"type": "number"}, "offset": {"type": "number"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_get_metadata", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}}, "required": ["page_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_update_page_settings", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "body": {"type": "object", "properties": {"id": {"type": "string"}, "siteId": {"type": "string"}, "title": {"type": "string"}, "slug": {"type": "string"}, "parentId": {"type": "string"}, "collectionId": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "lastUpdated": {"type": "string", "format": "date-time"}, "archived": {"type": "boolean"}, "draft": {"type": "boolean"}, "canBranch": {"type": "boolean"}, "isBranch": {"type": "boolean"}, "isMembersOnly": {"type": "boolean"}, "seo": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "additionalProperties": false}, "openGraph": {"type": "object", "properties": {"title": {"type": "string"}, "titleCopied": {"type": "boolean"}, "description": {"type": "string"}, "descriptionCopied": {"type": "boolean"}}, "additionalProperties": false}, "localeId": {"type": "string"}, "publishedPath": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["page_id", "body"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_get_content", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "limit": {"type": "number"}, "offset": {"type": "number"}}, "required": ["page_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "pages_update_static_content", "description": null, "input_schema": {"type": "object", "properties": {"page_id": {"type": "string"}, "localeId": {"type": "string"}, "nodes": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"nodeId": {"type": "string"}, "text": {"type": "string"}}, "required": ["nodeId", "text"], "additionalProperties": false}, {"type": "object", "properties": {"nodeId": {"type": "string"}, "propertyOverrides": {"type": "array", "items": {"type": "object", "properties": {"propertyId": {"type": "string"}, "text": {"type": "string"}}, "required": ["propertyId", "text"], "additionalProperties": false}}}, "required": ["nodeId", "propertyOverrides"], "additionalProperties": false}]}}}, "required": ["page_id", "localeId", "nodes"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_list", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}}, "required": ["site_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_get", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}}, "required": ["collection_id"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collections_create", "description": null, "input_schema": {"type": "object", "properties": {"site_id": {"type": "string"}, "request": {"type": "object", "properties": {"displayName": {"type": "string"}, "singularName": {"type": "string"}, "slug": {"type": "string"}}, "required": ["displayName", "singularName"], "additionalProperties": false}}, "required": ["site_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "collection_fields_create_static", "description": null, "input_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "request": {"type": "object", "properties": {"id": {"type": "string"}, "isEditable": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "type": {"type": "string", "enum": ["Color", "DateTime", "Email", "File", "Image", "Link", "MultiImage", "Number", "Phone", "PlainText", "RichText", "Switch", "Video"]}, "displayName": {"type": "string"}, "helpText": {"type": "string"}}, "required": ["type", "displayName"], "additionalProperties": false}}, "required": ["collection_id", "request"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}