{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "9d749227-a133-4307-b991-d454545bccb1", "name": "video-script-generation", "logo": null, "description": "An AI-powered server that outputs structured scenes with audio narration text and matching visual descriptions, ideal for automated video content creation.", "category": "general", "tags": null, "created_at": "2025-07-10T06:40:54.782141", "updated_at": "2025-08-18T10:25:33.421937", "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "hosted_url": "https://video-script-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["video_script_generate"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "video_script_generate", "description": "Generate a video script given a topic and video time (seconds)", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}, "video_time": {"title": "Video Time", "type": "integer"}, "scene_duration": {"default": 5, "title": "Scene Duration", "type": "integer"}}, "required": ["topic", "video_time"], "title": "VideoScriptInput", "type": "object"}, "output_schema": {"properties": {"audio_text": {"title": "Audio Text", "type": "string", "description": "The final, word-limited script text to be used for audio narration."}, "video_scenes": {"title": "Video Scenes", "type": "array", "items": {"type": "string"}, "description": "A list of scene descriptions for the video, synchronized with the audio text."}}, "required": ["audio_text", "video_scenes"], "title": "VideoScriptOutput", "type": "object"}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}