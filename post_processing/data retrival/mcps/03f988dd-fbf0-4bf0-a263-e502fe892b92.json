{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "name": "Jira & Confluence", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/jira.png/1751622867-jira.png", "description": "Jira & Confluence mcp for managing the tickets and pages", "category": "general", "tags": null, "created_at": "2025-07-04T05:58:16.021482", "updated_at": "2025-08-29T06:07:18.709037", "owner_id": "54c1059e-c891-4163-bbe6-1e5ad75601e1", "hosted_url": "https://jira-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["update_issue", "add_comment_with_attachment", "create_issue", "get_issue", "list_projects", "delete_issue", "search_issues", "attach_file", "transition_issue", "list_available_transitions", "add_comment", "attach_content", "create_issue_link", "get_user", "get_my_permissions", "update_start_end_date_time", "add_worklog", "list_sprints", "update_sprint_goal", "list_boards", "get_all_sprints", "get_sprint_by_id", "get_confluence_spaces", "get_confluence_page", "create_confluence_page", "confluence_search", "confluence_get_page_children", "confluence_get_page_ancestors", "confluence_get_comments", "confluence_update_page", "confluence_delete_page"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "update_issue", "description": "Update fields and/or change the status of a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue (e.g., 'TEST-48')."}, "summary": {"type": "string", "description": "New title (optional)."}, "description": {"type": "string", "description": "New plain text description (optional)."}, "priority": {"type": "string", "description": "New priority name (optional)."}, "assignee": {"type": "string", "description": "Assignee email or '' to unassign (optional)."}, "storyPoints": {"type": "number", "description": "New story point value (optional)."}, "sprintId": {"type": "number", "description": "Sprint ID to assign issue (optional)."}, "transitionTo": {"type": "string", "description": "Target status name (optional)."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {"properties": {"ghvvghv": {"type": "string", "description": "vghgvh", "title": "ghvvghv"}}}, "annotations": null}, {"name": "add_comment_with_attachment", "description": "Add a comment with a file attachment to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue."}, "comment": {"type": "string", "description": "The plain text content of the comment."}, "filename": {"type": "string", "description": "Name of the file to be attached."}, "filepath": {"type": "string", "description": "Local path to the file to attach."}}, "required": ["issue<PERSON><PERSON>", "comment", "filename", "filepath"]}, "output_schema": {"properties": {"jhbjbh": {"type": "string", "description": "hjbjh", "title": "jhbjbh"}}}, "annotations": null}, {"name": "create_issue", "description": "Create a new Jira issue.", "input_schema": {"type": "object", "properties": {"summary": {"type": "string", "description": "A concise title for the issue."}, "description": {"type": "string", "description": "A detailed plain text description of the issue."}, "issueType": {"type": "string", "description": "Type of issue (e.g., 'Task')."}, "projectKey": {"type": "string", "description": "Jira project key (e.g., 'TEST')."}, "priority": {"type": "string", "description": "Priority of the issue (e.g., 'High', 'Medium', 'Low'). Case-insensitive."}, "assignee": {"type": "string", "description": "Email, username, or account ID of the user to assign the issue to (optional)."}, "storyPoints": {"type": "number", "description": "Optional story point estimate for the issue (e.g., 3, 5, 8)."}, "sprintId": {"type": "number", "description": "Optional Sprint ID to add the issue to an active sprint."}}, "required": ["summary", "issueType", "projectKey"]}, "output_schema": {"properties": {"bhbhjbjb": {"type": "string", "description": "hbjb", "title": "bhbhjbjb"}}}, "annotations": null}, {"name": "get_issue", "description": "Get a Jira issue by key.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Jira issue key (e.g., 'TEST-1')"}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {}, "annotations": null}, {"name": "list_projects", "description": "List all Jira projects accessible to the user.", "input_schema": {"type": "object", "properties": {}}, "output_schema": null, "annotations": null}, {"name": "delete_issue", "description": "Delete a Jira issue or subtask", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to delete"}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": null, "annotations": null}, {"name": "search_issues", "description": "Search for issues in a project using JQL", "input_schema": {"type": "object", "properties": {"projectKey": {"type": "string", "description": "Project key (e.g., \"MRR\")"}, "jql": {"type": "string", "description": "JQL filter statement (e.g., status = 'To Do' AND priority = 'High')"}}, "required": ["projectKey", "jql"]}, "output_schema": {"properties": {"ghvvvh": {"type": "string", "description": "hjbbhjb", "title": "ghvvvh"}}}, "annotations": null}, {"name": "attach_file", "description": "Attaches a file to an existing Jira issue from a local path or a URL.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to attach the file to (e.g., 'TEST-34')."}, "filepath": {"type": "string", "description": "Local file path of the file to attach. Optional if 'file_url' is provided."}, "file_url": {"type": "string", "description": "URL of the file to attach. Optional if 'filepath' is provided."}}, "required": ["issue<PERSON><PERSON>"], "oneOf": [{"required": ["filepath"]}, {"required": ["file_url"]}]}, "output_schema": null, "annotations": null}, {"name": "transition_issue", "description": "Transitions a Jira issue to a new status (e.g., 'In Progress', 'Done').", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to transition (e.g., 'TEST-34')."}, "statusName": {"type": "string", "description": "The name of the transition action. IMPORTANT: This is the name of the action button you click in the Jira UI. It is case-sensitive and must match exactly. Common examples: 'Done', 'In Progress', 'To Do', 'Backlog'. Check the issue in Jira to see the available actions from its current status."}}, "required": ["issue<PERSON><PERSON>", "statusName"]}, "output_schema": {"properties": {"ghvvh": {"type": "string", "description": "hjjh", "title": "ghvvh"}}}, "annotations": null}, {"name": "list_available_transitions", "description": "Lists all possible transition actions for a given Jira issue from its current status.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "The key of the issue to check (e.g., 'TEST-34')."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": null, "annotations": null}, {"name": "add_comment", "description": "Add a comment to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to comment on (e.g., 'TEST-123')."}, "comment": {"type": "string", "description": "The plain text content of the comment."}}, "required": ["issue<PERSON><PERSON>", "comment"]}, "output_schema": {"properties": {"jvhjvh": {"type": "string", "description": "jhvhv", "title": "jvhjvh"}}}, "annotations": null}, {"name": "attach_content", "description": "Creates a file from provided content and attaches it to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to attach the content to."}, "filename": {"type": "string", "description": "The name of the attachment file as it will appear in Jira."}, "content": {"type": "string", "description": "The content to include in the attachment."}, "encoding": {"type": "string", "description": "Encoding of the content. Use 'base64' for binary data, 'none' for plain text.", "enum": ["none", "base64"], "default": "none"}}, "required": ["issue<PERSON><PERSON>", "filename", "content"]}, "output_schema": {"properties": {"hgvvh": {"type": "string", "description": "jhbbhj", "title": "hgvvh"}}}, "annotations": null}, {"name": "create_issue_link", "description": "Create a link between two existing Jira issues.", "input_schema": {"type": "object", "properties": {"inwardIssueKey": {"type": "string", "description": "The key of the issue that the link points TO. For a 'Blocks' link, this is the issue that IS BLOCKED."}, "outwardIssueKey": {"type": "string", "description": "The key of the issue that the link points FROM. For a 'Blocks' link, this is the issue that IS BLOCKING."}, "linkType": {"type": "string", "description": "The name of the link type (e.g., 'Blocks', 'Relates', 'Duplicates'). This is case-sensitive and must match a valid link type in your Jira instance."}}, "required": ["inwardIssueKey", "outwardIssueKey", "linkType"]}, "output_schema": null, "annotations": null}, {"name": "get_user", "description": "Get a user's account ID by email address", "input_schema": {"type": "object", "properties": {"email": {"type": "string", "description": "User's email address"}}, "required": ["email"]}, "output_schema": null, "annotations": null}, {"name": "get_my_permissions", "description": "Checks the abilities of the current API key by listing the permissions of the user account that owns the key. This is how you check the 'scopes' or 'limits' of your API token.", "input_schema": {"type": "object", "properties": {"projectKey": {"type": "string", "description": "Optional. The key of a project to check permissions for (e.g., 'TEST'). If you omit this, it will only check global permissions (like 'JIRA Administrator')."}}, "required": []}, "output_schema": null, "annotations": null}, {"name": "update_start_end_date_time", "description": "Update the start and end date of a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to update (e.g., 'TEST-48')."}, "startDate": {"type": "string", "description": "New start date in YYYY-MM-DD format (optional)."}, "endDate": {"type": "string", "description": "New end date in YYYY-MM-DD format (optional)."}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {"properties": {"ghgvhgh": {"type": "string", "description": "jvghvhb", "title": "ghgvhgh"}}}, "annotations": null}, {"name": "add_worklog", "description": "Adds a worklog to a Jira issue.", "input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Key of the issue to add the worklog to (e.g., 'TEST-48')."}, "timeSpent": {"type": "string", "description": "The amount of time spent, e.g., '1w 2d 3h 4m'."}, "comment": {"type": "string", "description": "A comment to add to the worklog (optional)."}}, "required": ["issue<PERSON><PERSON>", "timeSpent"]}, "output_schema": null, "annotations": null}, {"name": "list_sprints", "description": "List all sprints for a given Jira board.", "input_schema": {"type": "object", "properties": {"boardId": {"type": "string", "description": "The ID of the board to get sprints from."}}, "required": ["boardId"]}, "output_schema": {}, "annotations": null}, {"name": "update_sprint_goal", "description": "Update the goal of a sprint.", "input_schema": {"type": "object", "properties": {"sprintId": {"type": "string", "description": "The ID of the sprint to update."}, "goal": {"type": "string", "description": "The new goal for the sprint."}}, "required": ["sprintId", "goal"]}, "output_schema": {"properties": {"hgvvhv": {"type": "string", "description": "jhvgvhvhv", "title": "hgvvhv"}}}, "annotations": null}, {"name": "list_boards", "description": "List all Jira boards accessible to the user.", "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": {"properties": {"boards": {"type": "string", "description": "boards", "title": "boards"}}}, "annotations": null}, {"name": "get_all_sprints", "description": "Get all sprints across all Jira boards accessible to the user.", "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": {"properties": {"sprints": {"type": "string", "description": "sprints", "title": "sprints"}}}, "annotations": null}, {"name": "get_sprint_by_id", "description": "Get details of a specific Jira sprint by its ID.", "input_schema": {"type": "object", "properties": {"sprintId": {"type": "string", "description": "The ID of the sprint to retrieve."}}, "required": ["sprintId"]}, "output_schema": null, "annotations": null}, {"name": "get_confluence_spaces", "description": "Get all Confluence spaces.", "input_schema": {"type": "object", "properties": {"limit": {"type": "integer", "description": "Maximum number of spaces to return", "default": 30}}}, "output_schema": null, "annotations": null}, {"name": "get_confluence_page", "description": "Get a Confluence page by ID.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "create_confluence_page", "description": "Create a new Confluence page.", "input_schema": {"type": "object", "properties": {"spaceId": {"type": "string", "description": "Confluence space ID. Either spaceId or spaceKey is required."}, "spaceKey": {"type": "string", "description": "Confluence space key. Either spaceId or spaceKey is required."}, "title": {"type": "string", "description": "Page title"}, "body": {"type": "string", "description": "Page content in storage format"}}, "required": ["title", "body"]}, "output_schema": null, "annotations": null}, {"name": "confluence_search", "description": "Search Confluence content using simple terms or CQL (Confluence Query Language).", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query or CQL expression"}}, "required": ["query"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_page_children", "description": "Get the list of child pages for a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_page_ancestors", "description": "Retrieve the ancestor (parent) pages of a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_get_comments", "description": "Get all comments associated with a specific Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": null, "annotations": null}, {"name": "confluence_update_page", "description": "Update the content or metadata of an existing Confluence page.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}, "title": {"type": "string", "description": "New page title"}, "body": {"type": "string", "description": "New page content in storage format"}}, "required": ["pageId"]}, "output_schema": {"properties": {"Abc": {"type": "string", "description": "xyz", "title": "Abc"}}}, "annotations": null}, {"name": "confluence_delete_page", "description": "Delete a Confluence page permanently.", "input_schema": {"type": "object", "properties": {"pageId": {"type": "string", "description": "Confluence page ID"}}, "required": ["pageId"]}, "output_schema": {"properties": {"hjvh": {"type": "string", "description": "jhbbjh", "title": "hjvh"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["742013b1-2739-43d5-9d38-391c8f3d15a8"], "url": null}}