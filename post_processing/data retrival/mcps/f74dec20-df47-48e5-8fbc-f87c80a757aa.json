{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "f74dec20-df47-48e5-8fbc-f87c80a757aa", "name": "<PERSON><PERSON><PERSON>", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/twilo.png/1750338945-twilo.png", "description": "A Model Context Protocol (MCP) server that enables Claude and other AI assistants to send SMS messages using Twilio.", "category": "general", "tags": null, "created_at": "2025-06-18T12:27:58.293042", "updated_at": "2025-08-28T13:29:30.651778", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://twilio-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["send_sms"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "send_sms", "description": "Send sms to the user using Twilio", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["to", "body"], "title": "SendSMS", "type": "object"}, "output_schema": {"type": "object", "properties": {"status": {"type": "string", "description": "The delivery status of the SMS, such as 'sent', 'delivered', or 'failed'", "title": "status"}, "messageId": {"type": "string", "description": "A unique identifier for the SMS message, useful for tracking", "title": "messageId"}, "to": {"type": "string", "description": "The recipient phone number the SMS was sent to", "title": "to"}, "body": {"type": "string", "description": "The message content that was sent", "title": "body"}, "error": {"type": "string", "description": "Error message if the SMS failed to send", "title": "error"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}