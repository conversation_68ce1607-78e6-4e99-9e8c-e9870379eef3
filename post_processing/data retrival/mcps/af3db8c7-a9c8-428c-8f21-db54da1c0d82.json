{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "af3db8c7-a9c8-428c-8f21-db54da1c0d82", "name": "<PERSON><PERSON><PERSON>", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/sendgrid.png/1750338844-sendgrid.png", "description": "The SendGrid Email MCP Server is an AI-compatible service that allows agents or users to send emails using the SendGrid API. It supports dynamic templates, HTML/plain text emails. ", "category": "general", "tags": null, "created_at": "2025-06-18T06:09:32.672769", "updated_at": "2025-08-25T12:27:06.059662", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://sendgrid-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["send_bulk_mail", "send_mail"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "send_bulk_mail", "description": "Send bulk mail to multiple recipients using SendGrid", "input_schema": {"properties": {"recipients": {"items": {"type": "string"}, "title": "Recipients", "type": "array"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["recipients", "subject", "body"], "title": "SendBulkMail", "type": "object"}, "output_schema": {"properties": {"url": {"type": "string", "description": "url link", "title": "url"}}}, "annotations": null}, {"name": "send_mail", "description": "Send mail to the user using SendGrid", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["to", "subject", "body"], "title": "SendMail", "type": "object"}, "output_schema": {"properties": {"result": {"type": "string", "description": "result", "title": "result"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}