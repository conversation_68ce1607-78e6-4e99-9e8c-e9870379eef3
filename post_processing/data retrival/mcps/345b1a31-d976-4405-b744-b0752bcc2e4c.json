{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "name": "Google Sheets", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Sheets_Logo_512px.png/1750857479-Google_Sheets_Logo_512px.png", "description": "This MCP server integrates with your Google Sheets, to enable creating and modifying spreadsheets.", "category": "general", "tags": null, "created_at": "2025-06-25T13:16:18.995231", "updated_at": "2025-08-26T08:18:29.456373", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://google-sheets-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["insert_row", "create_column", "get_cell", "find_empty_row", "insert_multiple_rows", "find_row", "count_column_values", "set_formula", "update_cell", "add_multiple_rows", "add_single_row", "clear_cell", "clear_rows", "create_spreadsheet", "insert_anchored_note", "delete_rows", "list_worksheets", "update_multiple_rows", "delete_worksheet", "get_spreadsheet_by_id", "get_values_in_range", "create_worksheet", "upsert_row", "copy_worksheet", "update_row", "insert_comment"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "insert_row", "description": "Insert a single row at the specified index, shifting existing rows down", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"title": "Row Index", "type": "integer"}}, "required": ["spreadsheet_id", "row", "row_index"], "title": "InsertRow", "type": "object"}, "output_schema": {"properties": {"value": {"type": "string", "description": "save", "title": "value"}}}, "annotations": null}, {"name": "create_column", "description": "Create a new column in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Column Index"}, "values": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Values"}}, "required": ["spreadsheet_id"], "title": "CreateColumn", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_cell", "description": "Fetch the contents of a specific cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}}, "required": ["spreadsheet_id", "cell"], "title": "GetCell", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_empty_row", "description": "Find the first empty row in a worksheet starting from a specific row", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Start Row"}, "search_column": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "A", "title": "Search Column"}}, "required": ["spreadsheet_id"], "title": "FindEmptyRow", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "insert_multiple_rows", "description": "Insert multiple rows at the specified index, shifting existing rows down", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "rows": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Rows", "type": "array"}, "start_row_index": {"title": "Start Row Index", "type": "integer"}}, "required": ["spreadsheet_id", "rows", "start_row_index"], "title": "InsertMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_row", "description": "Find one or more rows by a column and value", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "count_column_values", "description": "Count the total number of values in a specific column", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "column": {"title": "Column", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Value"}, "include_empty": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Empty"}}, "required": ["spreadsheet_id", "column"], "title": "CountCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "set_formula", "description": "Set a formula in a specific cell of a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "formula": {"title": "Formula", "type": "string"}}, "required": ["spreadsheet_id", "cell", "formula"], "title": "SetForm<PERSON>", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "update_cell", "description": "Update a cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "add_multiple_rows", "description": "Add multiple rows of data to a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "rows": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Rows", "type": "array"}, "start_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Start Row"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "rows"], "title": "AddMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_single_row", "description": "Add a single row of data to Google Sheets", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "clear_cell", "description": "Delete the content of a specific cell in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}}, "required": ["spreadsheet_id", "cell"], "title": "ClearCell", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "clear_rows", "description": "Delete the content of a row or rows in a spreadsheet. Deleted rows will appear as blank rows", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"title": "Start Row", "type": "integer"}, "end_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "End Row"}}, "required": ["spreadsheet_id", "start_row"], "title": "ClearRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_spreadsheet", "description": "Create a blank spreadsheet or duplicate an existing spreadsheet", "input_schema": {"properties": {"title": {"title": "Title", "type": "string"}, "worksheet_titles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Worksheet Titles"}}, "required": ["title"], "title": "CreateSpreadsheet", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "insert_anchored_note", "description": "Insert a note on a spreadsheet cell", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "note_text": {"title": "Note Text", "type": "string"}}, "required": ["spreadsheet_id", "cell", "note_text"], "title": "InsertAnchoredNote", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_rows", "description": "Deletes the specified rows from a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "start_row": {"title": "Start Row", "type": "integer"}, "end_row": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "End Row"}}, "required": ["spreadsheet_id", "start_row"], "title": "DeleteRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_worksheets", "description": "Get a list of all worksheets in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}}, "required": ["spreadsheet_id"], "title": "ListWorksheets", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_multiple_rows", "description": "Update multiple rows in a spreadsheet defined by a range", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "range_notation": {"title": "Range Notation", "type": "string"}, "values": {"items": {"items": {"type": "string"}, "type": "array"}, "title": "Values", "type": "array"}}, "required": ["spreadsheet_id", "range_notation", "values"], "title": "UpdateMultipleRows", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_worksheet", "description": "Delete a specific worksheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"title": "Worksheet Name", "type": "string"}}, "required": ["spreadsheet_id", "worksheet_name"], "title": "DeleteWorksheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_spreadsheet_by_id", "description": "Returns the spreadsheet at the given ID", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "include_grid_data": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Include Grid Data"}}, "required": ["spreadsheet_id"], "title": "GetSpreadsheetById", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "get_values_in_range", "description": "Get all values or values from a range of cells using A1 notation", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "range_notation": {"title": "Range Notation", "type": "string"}, "value_render_option": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "FORMATTED_VALUE", "title": "Value Render Option"}}, "required": ["spreadsheet_id", "range_notation"], "title": "GetValuesInRange", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_worksheet", "description": "Create a blank worksheet with a title", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "title": {"title": "Title", "type": "string"}, "row_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1000, "title": "Row Count"}, "column_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 26, "title": "Column Count"}}, "required": ["spreadsheet_id", "title"], "title": "CreateWorksheet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upsert_row", "description": "Upsert a row of data in a Google Sheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "key_column": {"title": "Key Column", "type": "string"}, "key_value": {"title": "Key Value", "type": "string"}}, "required": ["spreadsheet_id", "row", "key_column", "key_value"], "title": "UpsertRow", "type": "object"}, "output_schema": {"properties": {"row_id": {"type": "string", "description": "id of the row", "title": "row_id"}}}, "annotations": null}, {"name": "copy_worksheet", "description": "Copy an existing worksheet to another Google Sheets file", "input_schema": {"properties": {"source_spreadsheet_id": {"title": "Source Spreadsheet Id", "type": "string"}, "source_worksheet_name": {"title": "Source Worksheet Name", "type": "string"}, "destination_spreadsheet_id": {"title": "Destination Spreadsheet Id", "type": "string"}, "destination_worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Destination Worksheet Name"}}, "required": ["source_spreadsheet_id", "source_worksheet_name", "destination_spreadsheet_id"], "title": "CopyWorksheet", "type": "object"}, "output_schema": {"properties": {"id": {"type": "string", "description": "id", "title": "id"}}}, "annotations": null}, {"name": "update_row", "description": "Update a row in a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row_index": {"title": "Row Index", "type": "integer"}, "values": {"items": {"type": "string"}, "title": "Values", "type": "array"}}, "required": ["spreadsheet_id", "row_index", "values"], "title": "UpdateRow", "type": "object"}, "output_schema": {"properties": {"output": {"type": "string", "description": "output", "title": "output"}}}, "annotations": null}, {"name": "insert_comment", "description": "Insert a comment into a spreadsheet", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "comment_text": {"title": "Comment Text", "type": "string"}}, "required": ["spreadsheet_id", "cell", "comment_text"], "title": "InsertComment", "type": "object"}, "output_schema": {"properties": {"message": {"type": "string", "description": "message", "title": "message"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["87b72e0c-e890-4ef5-bccf-7c783c1fb2bc"], "url": null}}