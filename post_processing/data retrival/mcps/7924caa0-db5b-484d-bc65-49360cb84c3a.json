{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "name": "Google Calendar", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/google_calendar.png/1750839139-google_calendar.png", "description": "Google Calendar MCP Server to manage the events from the calendar of the user.", "category": "general", "tags": null, "created_at": "2025-06-24T13:04:59.545628", "updated_at": "2025-08-26T12:45:43.547842", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://google-calendar-mcp-server-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["fetch_google_meeting", "update_google_meeting", "delete_google_meeting", "validate_google_credentials", "create_google_meeting"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "fetch_google_meeting", "description": "Fetch Google Calendar data - either a specific meeting by ID or multiple events with optional time filtering", "input_schema": {"examples": [{"description": "Fetch a specific meeting by ID", "meeting_id": "abc123def456ghi789"}, {"description": "Fetch multiple events with time filtering", "max_results": 25, "time_max": "2024-01-31T23:59:59", "time_min": "2024-01-01T00:00:00"}], "properties": {"meeting_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Meeting Id"}, "time_min": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time Min"}, "time_max": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time Max"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "title": "FetchGoogleMeeting", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "update_google_meeting", "description": "Update an existing Google Calendar event", "input_schema": {"example": {"attendees": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "description": "Updated weekly sprint planning meeting with new agenda", "end_time": "2024-01-15T10:30:00", "meeting_id": "abc123def456ghi789", "start_time": "2024-01-15T09:30:00", "title": "Updated: Team Sprint Planning"}, "properties": {"meeting_id": {"title": "Meeting Id", "type": "string"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "End Time"}, "attendees": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Attendees"}}, "required": ["meeting_id"], "title": "UpdateGoogleMeeting", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_google_meeting", "description": "Delete an existing Google Calendar event", "input_schema": {"example": {"meeting_id": "abc123def456ghi789"}, "properties": {"meeting_id": {"title": "Meeting Id", "type": "string"}}, "required": ["meeting_id"], "title": "DeleteGoogleMeeting", "type": "object"}, "output_schema": {"properties": {"ghghf": {"type": "string", "description": "grrgd", "title": "ghghf"}}}, "annotations": null}, {"name": "validate_google_credentials", "description": "Validate Google OAuth credentials and check API access", "input_schema": {"example": {}, "properties": {}, "title": "ValidateGoogleCredentials", "type": "object"}, "output_schema": {"properties": {"jvjvbjhv": {"type": "string", "description": "gjvvhvjvh", "title": "jvjvbjhv"}}}, "annotations": null}, {"name": "create_google_meeting", "description": "Create a new Google Calendar event with Google Meet link", "input_schema": {"example": {"attendees": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "description": "Weekly sprint planning meeting for the development team", "end_time": "2024-01-15T10:00:00", "start_time": "2024-01-15T09:00:00", "title": "Team Sprint Planning"}, "properties": {"title": {"title": "Title", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "start_time": {"title": "Start Time", "type": "string"}, "end_time": {"title": "End Time", "type": "string"}, "attendees": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": [], "title": "Attendees"}}, "required": ["title", "start_time", "end_time"], "title": "CreateGoogleMeeting", "type": "object"}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["207c2d10-e2e1-46b5-b702-3d03d7e04eed"], "url": null}}