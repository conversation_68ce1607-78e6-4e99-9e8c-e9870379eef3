{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "06df097c-d38f-443b-a57b-886a9b14b1b2", "name": "mem0-mcp", "logo": null, "description": "Store and retrieve user-specific memories to maintain context and make informed decisions based on past interactions. Utilize a simple API to add and search memories with relevance scoring, enhancing ", "category": "engineering", "tags": ["Memory Storage"], "created_at": "2025-06-26T12:11:24.317116", "updated_at": "2025-08-18T06:48:19.711919", "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "hosted_url": "https://server.smithery.ai/@mem0ai/mem0-memory-mcp/mcp?api_key=6495e262-6eb8-4845-bbb8-ca2db907d7ea&profile=weekly-limpet-QXWEEZ", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["add-memory", "search-memories"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "add-memory", "description": "Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevent information whcih can be useful in the future conversation. This can also be called when the user asks you to remember something.", "input_schema": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to store in memory"}, "userId": {"type": "string", "description": "User ID for memory storage. If not provided explicitly, use a generic user ID like, 'mem0-mcp-user'"}}, "required": ["content", "userId"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}, "annotations": null}, {"name": "search-memories", "description": "Search through stored memories. This method is called ANYTIME the user asks anything.", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query. This is the query that the user has asked for. Example: 'What did I tell you about the weather last week?' or 'What did I tell you about my friend <PERSON>?'"}, "userId": {"type": "string", "description": "User ID for memory storage. If not provided explicitly, use a generic user ID like, 'mem0-mcp-user"}}, "required": ["query", "userId"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}