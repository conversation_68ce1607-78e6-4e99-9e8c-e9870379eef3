{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "name": "ZoHo CRM", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/filters_no_upscale%28%29.png/1751866091-filters_no_upscale.png", "description": "zoho crm mcp tool for performing CRM related tasks", "category": "general", "tags": null, "created_at": "2025-07-07T05:28:18.079514", "updated_at": "2025-08-15T08:16:32.199644", "owner_id": "6e75537b-e7dd-4f2e-9455-2b9acce6f351", "hosted_url": "https://zoho-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["create_record", "get_records", "search_records", "health_check", "get_tool_info"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_record", "description": "Create a new record in a Zoho CRM module. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)", "input_schema": {"description": "Schema for creating a new Zoho CRM record", "properties": {"module": {"description": "Target Zoho CRM module", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"], "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "record_data": {"additionalProperties": true, "description": "Field/value pairs for the new record. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)", "title": "Record Data", "type": "object"}}, "required": ["module", "record_data"], "title": "CreateRecord", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_records", "description": "Retrieve records from Zoho CRM with optional field filtering and pagination", "input_schema": {"description": "Schema for retrieving Zoho CRM records", "properties": {"module": {"description": "Target Zoho CRM module", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"], "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Array of field names to retrieve (if not provided, returns all fields)", "title": "Fields"}, "page": {"anyOf": [{"minimum": 1, "type": "integer"}, {"type": "null"}], "default": 1, "description": "Page number (minimum: 1)", "title": "Page"}, "per_page": {"anyOf": [{"maximum": 200, "minimum": 1, "type": "integer"}, {"type": "null"}], "default": 50, "description": "Records per page (minimum: 1, maximum: 200)", "title": "Per Page"}}, "required": ["module"], "title": "GetRecords", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "search_records", "description": "Search records using Zoho-style criteria with advanced filtering", "input_schema": {"description": "Schema for searching Zoho CRM records", "properties": {"module": {"description": "Target Zoho CRM module", "enum": ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events", "Calls"], "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "search_criteria": {"description": "Search criteria in Zoho format: (Field_Name:operator:value). Examples: (Last_Name:contains:<PERSON>), ((Last_Name:contains:<PERSON>)and(Lead_Source:equals:Website))", "minLength": 3, "title": "Search Criteria", "type": "string"}, "page": {"anyOf": [{"minimum": 1, "type": "integer"}, {"type": "null"}], "default": 1, "description": "Page number (minimum: 1)", "title": "Page"}, "per_page": {"anyOf": [{"maximum": 200, "minimum": 1, "type": "integer"}, {"type": "null"}], "default": 200, "description": "Records per page (minimum: 1, maximum: 200)", "title": "Per Page"}}, "required": ["module", "search_criteria"], "title": "SearchRecords", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "health_check", "description": "Verify server health and Zoho connectivity", "input_schema": {"description": "Schema for health check - no parameters required", "properties": {}, "title": "HealthCheck", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_tool_info", "description": "Return detailed parameter specs for any tool", "input_schema": {"description": "Schema for getting tool information", "properties": {"tool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Specific tool name to get information for", "enum": ["create_record", "get_records", "search_records", "health_check", "get_tool_info"], "title": "Tool Name"}}, "title": "GetToolInfo", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["26c77e5b-fdb9-42a9-9c85-8d96c687d2f0"], "url": null}}