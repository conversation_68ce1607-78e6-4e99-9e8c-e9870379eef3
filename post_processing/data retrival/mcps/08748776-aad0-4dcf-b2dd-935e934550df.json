{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "08748776-aad0-4dcf-b2dd-935e934550df", "name": "Postgres", "logo": null, "description": null, "category": "general", "tags": null, "created_at": "2025-07-04T06:15:18.126281", "updated_at": "2025-08-05T10:31:04.016165", "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "hosted_url": "https://server.smithery.ai/@gldc/mcp-postgres/mcp?api_key=1fa48f2e-e939-4241-8f87-53765c0c1f8e&profile=doubtful-kangaroo-G6kIQG", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["query", "list_schemas", "list_tables", "describe_table", "get_foreign_keys", "find_relationships"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "query", "description": "Execute a SQL query against the PostgreSQL database.", "input_schema": {"properties": {"sql": {"title": "Sql", "type": "string"}, "parameters": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "title": "Parameters"}}, "required": ["sql"], "title": "queryArguments", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "list_schemas", "description": "List all schemas in the database.", "input_schema": {"properties": {}, "title": "list_schemasArguments", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "list_tables", "description": "List all tables in a specific schema.\n    \n    Args:\n        db_schema: The schema name to list tables from (defaults to 'public')\n    ", "input_schema": {"properties": {"db_schema": {"default": "public", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "type": "string"}}, "title": "list_tablesArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "describe_table", "description": "Get detailed information about a table.\n    \n    Args:\n        table_name: The name of the table to describe\n        db_schema: The schema name (defaults to 'public')\n    ", "input_schema": {"properties": {"table_name": {"title": "Table Name", "type": "string"}, "db_schema": {"default": "public", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["table_name"], "title": "describe_tableArguments", "type": "object"}, "output_schema": {"properties": {"answer": {"type": "string", "description": "the answer for the query", "title": "answer"}}}, "annotations": null}, {"name": "get_foreign_keys", "description": "Get foreign key information for a table.\n    \n    Args:\n        table_name: The name of the table to get foreign keys from\n        db_schema: The schema name (defaults to 'public')\n    ", "input_schema": {"properties": {"table_name": {"title": "Table Name", "type": "string"}, "db_schema": {"default": "public", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["table_name"], "title": "get_foreign_keysArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "find_relationships", "description": "Find both explicit and implied relationships for a table.\n    \n    Args:\n        table_name: The name of the table to analyze relationships for\n        db_schema: The schema name (defaults to 'public')\n    ", "input_schema": {"properties": {"table_name": {"title": "Table Name", "type": "string"}, "db_schema": {"default": "public", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["table_name"], "title": "find_relationshipsArguments", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}