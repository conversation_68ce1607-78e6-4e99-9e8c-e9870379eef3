{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "name": "Gmail", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/gmail.png/1750845179-gmail.png", "description": "The server provides tools to retrieve, read, send, view, and remove emails.", "category": "general", "tags": null, "created_at": "2025-06-25T09:53:04.457157", "updated_at": "2025-08-27T11:02:01.789159", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://gmail-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["add_label_to_email", "archive_email", "delete_email", "create_draft", "create_draft_reply", "create_label", "send_email", "remove_label_from_email", "remove_label_from_conversation", "reply_to_email", "find_email", "find_or_send_email"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "add_label_to_email", "description": "Add a label to an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["message_id"], "title": "AddLabelToEmail", "type": "object"}, "output_schema": {"properties": {"test ": {"type": "string", "description": "test ", "title": "test "}}}, "annotations": null}, {"name": "archive_email", "description": "Archive an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}}, "required": ["message_id"], "title": "ArchiveEmail", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete_email", "description": "Send an email message to the trash", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}}, "required": ["message_id"], "title": "DeleteEmail", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_draft", "description": "Create a draft email message", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "CreateDraft", "type": "object"}, "output_schema": {"properties": {"bhjbhj": {"type": "string", "description": "bhhbbjb", "title": "bhjbhj"}}}, "annotations": null}, {"name": "create_draft_reply", "description": "Create a draft reply to an existing email", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "body": {"title": "Body", "type": "string"}, "include_original": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Include Original"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["message_id", "body"], "title": "CreateDraftReply", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_label", "description": "Create a new label in Gmail", "input_schema": {"properties": {"label_name": {"title": "Label Name", "type": "string"}, "label_list_visibility": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "labelShow", "title": "Label List Visibility"}, "message_list_visibility": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "show", "title": "Message List Visibility"}}, "required": ["label_name"], "title": "CreateLabel", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "send_email", "description": "Create and send a new email message", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "SendEmail", "type": "object"}, "output_schema": {"properties": {"body": {"type": "string", "description": "body", "title": "body"}}}, "annotations": null}, {"name": "remove_label_from_email", "description": "Remove a label from an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["message_id"], "title": "RemoveLabelFromEmail", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "remove_label_from_conversation", "description": "Remove a specified label from all emails within a conversation", "input_schema": {"properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "label_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Id"}, "label_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Label Name"}}, "required": ["conversation_id"], "title": "RemoveLabelFromConversation", "type": "object"}, "output_schema": {"properties": {"message": {"type": "string", "description": "message", "title": "message"}}}, "annotations": null}, {"name": "reply_to_email", "description": "Send a reply to an email message", "input_schema": {"properties": {"message_id": {"title": "Message Id", "type": "string"}, "body": {"title": "Body", "type": "string"}, "include_original": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Include Original"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["message_id", "body"], "title": "ReplyToEmail", "type": "object"}, "output_schema": {"properties": {"message": {"type": "string", "description": "message", "title": "message"}}}, "annotations": null}, {"name": "find_email", "description": "Find an email message based on search query", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["query"], "title": "FindEmail", "type": "object"}, "output_schema": {"properties": {"message": {"type": "string", "description": "message", "title": "message"}}}, "annotations": null}, {"name": "find_or_send_email", "description": "Find a specific message or create and send a new one if not found", "input_schema": {"properties": {"search_query": {"title": "Search Query", "type": "string"}, "to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["search_query", "to", "subject", "body"], "title": "FindOrSendEmail", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["20cebfff-1435-4081-90df-90a149f41194"], "url": null}}