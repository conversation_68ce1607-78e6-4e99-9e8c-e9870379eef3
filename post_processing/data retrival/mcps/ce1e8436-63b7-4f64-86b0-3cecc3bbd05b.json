{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "ce1e8436-63b7-4f64-86b0-3cecc3bbd05b", "name": "script-generation-mcp-server", "logo": null, "description": "This server provides an API for generating custom scripts using OpenAI's GPT-4o model.\nUsers can specify parameters such as script type, tone, length, topic, characters, scene, and call-to-action.", "category": "general", "tags": ["video content", "automation"], "created_at": "2025-07-16T05:37:19.780052", "updated_at": "2025-08-13T06:19:06.140863", "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "hosted_url": "https://script-generation-mcp-server-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_script"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_script", "description": "Generate a script using OpenAI GPT-4o.", "input_schema": {"properties": {"script_type": {"description": "Type of script to generate (e.g., 'Ad', 'Story', etc.)", "title": "Script Type", "type": "string"}, "tone": {"description": "Tone of the script (e.g., 'Professional', 'Casual', etc.)", "title": "<PERSON><PERSON>", "type": "string"}, "length": {"anyOf": [{"type": "integer"}, {"type": "string"}], "description": "Total script duration in seconds. Can be an integer or a string containing only digits.", "title": "Length"}, "topic": {"description": "Main Topic of the script.", "title": "Topic", "type": "string"}, "characters": {"description": "Characters to include. Enter 'nil' to opt out.", "title": "Characters", "type": "string"}, "scene": {"description": "Specific scene description to be included in the script. Enter 'nil' to opt out.", "title": "Scene", "type": "string"}, "cta": {"description": "Call-to-action line. Enter 'nil' to opt out.", "title": "Cta", "type": "string"}, "scene_duration": {"anyOf": [{"type": "integer"}, {"type": "string"}, {"type": "null"}], "default": null, "description": "Duration in seconds for each scene/part. Can be an integer or a string containing only digits. If equal to length, only one part will be generated. Optional.", "title": "Scene Duration"}}, "required": ["script_type", "tone", "length", "topic", "characters", "scene", "cta"], "title": "ScriptInput", "type": "object"}, "output_schema": {"properties": {"script": {"type": "array", "description": "A list of scene objects, each with a scene label and its cinematic visual description. Contains only visual elements without any sound or audio descriptions.", "title": "script", "items": {"type": "object", "description": "A scene object with a single key (scene label) and its visual content as the value."}}, "audio_text": {"type": "string", "description": "The voiceover-only text for audio generation.", "title": "audio_text"}, "sound_effects": {"type": "array", "description": "A list of scene objects, each with a scene label and its detailed sound effects including ambient sounds, action sounds, and audio cues.", "title": "sound_effects", "items": {"type": "object", "description": "A scene object with a single key (scene label) and its sound effects as the value."}}, "title": {"type": "string", "description": "A concise title for the script, maximum 6-7 words.", "title": "title"}, "error": {"type": "string", "description": "Error message if script generation failed.", "title": "error"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}