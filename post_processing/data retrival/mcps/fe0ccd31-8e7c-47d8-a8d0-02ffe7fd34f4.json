{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "name": "Tavily Web Search and Extraction Server", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/tavily.png/1750839496-tavily.png", "description": "Enable AI assistants to perform real-time web searches, extract data from web pages, map website structures, and crawl websites systematically. Enhance your AI's capabilities with powerful tools for intelligent data retrieval and analysis from the web. Seamlessly integrate advanced search and extraction features into your MCP client workflows.", "category": "general", "tags": ["search"], "created_at": "2025-06-14T07:34:51.848135", "updated_at": "2025-08-31T07:18:24.698533", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/Jeetanshu18/tavily-mcp", "api_documentation": null, "capabilities": ["tavily-extract", "tavily-crawl", "tavily-search", "tavily-map"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "tavily-extract", "description": "A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks.", "input_schema": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}, "description": "List of URLs to extract content from"}, "extract_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "Depth of extraction - 'basic' or 'advanced', if usrls are linkedin use 'advanced' or if explicitly told to use advanced", "default": "basic"}, "include_images": {"type": "boolean", "description": "Include a list of images extracted from the urls in the response", "default": false}}, "required": ["urls"]}, "output_schema": {"properties": {"generated_string_output": {"type": "string", "description": "the value which is created from tavily", "title": "generated_string_output"}}}, "annotations": null}, {"name": "tavily-crawl", "description": "A powerful web crawler that initiates a structured web crawl starting from a specified base URL. The crawler expands from that point like a tree, following internal links across pages. You can control how deep and wide it goes, and guide it to focus on specific sections of the site.", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The root URL to begin the crawl"}, "max_depth": {"type": "integer", "description": "Max depth of the crawl. Defines how far from the base URL the crawler can explore.", "default": 1, "minimum": 1}, "max_breadth": {"type": "integer", "description": "Max number of links to follow per level of the tree (i.e., per page)", "default": 20, "minimum": 1}, "limit": {"type": "integer", "description": "Total number of links the crawler will process before stopping", "default": 50, "minimum": 1}, "instructions": {"type": "string", "description": "Natural language instructions for the crawler"}, "select_paths": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select only URLs with specific path patterns (e.g., /docs/.*, /api/v1.*)", "default": []}, "select_domains": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select crawling to specific domains or subdomains (e.g., ^docs\\.example\\.com$)", "default": []}, "allow_external": {"type": "boolean", "description": "Whether to allow following links that go to external domains", "default": false}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Careers", "Blog", "Documentation", "About", "Pricing", "Community", "Developers", "Contact", "Media"]}, "description": "Filter URLs using predefined categories like documentation, blog, api, etc", "default": []}, "extract_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "Advanced extraction retrieves more data, including tables and embedded content, with higher success but may increase latency", "default": "basic"}}, "required": ["url"]}, "output_schema": {"properties": {"generated_string_output": {"type": "string", "description": "the value which is created from tavily", "title": "generated_string_output"}}}, "annotations": null}, {"name": "tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"type": "object", "properties": {"content": {"type": "string", "description": "generated content from tavily", "title": "content"}}, "required": ["content"]}, "annotations": null}, {"name": "tavily-map", "description": "A powerful web mapping tool that creates a structured map of website URLs, allowing you to discover and analyze site structure, content organization, and navigation paths. Perfect for site audits, content discovery, and understanding website architecture.", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The root URL to begin the mapping"}, "max_depth": {"type": "integer", "description": "Max depth of the mapping. Defines how far from the base URL the crawler can explore", "default": 1, "minimum": 1}, "max_breadth": {"type": "integer", "description": "Max number of links to follow per level of the tree (i.e., per page)", "default": 20, "minimum": 1}, "limit": {"type": "integer", "description": "Total number of links the crawler will process before stopping", "default": 50, "minimum": 1}, "instructions": {"type": "string", "description": "Natural language instructions for the crawler"}, "select_paths": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select only URLs with specific path patterns (e.g., /docs/.*, /api/v1.*)", "default": []}, "select_domains": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select crawling to specific domains or subdomains (e.g., ^docs\\.example\\.com$)", "default": []}, "allow_external": {"type": "boolean", "description": "Whether to allow following links that go to external domains", "default": false}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Careers", "Blog", "Documentation", "About", "Pricing", "Community", "Developers", "Contact", "Media"]}, "description": "Filter URLs using predefined categories like documentation, blog, api, etc", "default": []}}, "required": ["url"]}, "output_schema": {"properties": {"vjhvvjh": {"type": "string", "description": "jvhhvhv", "title": "vjhvvjh"}, "vghhvg": {"type": "string", "description": "hgvv", "title": "vghhvg"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "tavily-mcp", "git_user_name": "Jeetanshu18", "integrations": ["49d6dfb0-22d7-4dd7-a1ca-d5a9c7f07d6a"], "url": null}}