{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "1d0c7895-4304-4e49-9d6c-e1623588beea", "name": "Send Issue Review", "description": "Send_Issue_Review", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/556523b1-ce87-43cd-9aac-c1b9f21f30dc.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/889a45d5-1242-4ef9-814a-06d9d309d902.json", "start_nodes": [{"field": "issue<PERSON><PERSON>", "type": "string", "transition_id": "transition-MCP_<PERSON>ra_&_Confluence_get_issue-1753795019023"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-29T13:23:35.141867", "updated_at": "2025-08-22T04:51:15.977717", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Jira_&_Confluence_get_issue", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "transition_id": "transition-MCP_<PERSON>ra_&_Confluence_get_issue-1753795019023", "type": "mcp", "display_name": "Jira & Confluence - get_issue", "label": "Jira & Confluence - get_issue", "data": {"input_schema": {"type": "object", "properties": {"issueKey": {"type": "string", "description": "Jira issue key (e.g., 'TEST-1')"}}, "required": ["issue<PERSON><PERSON>"]}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753794998297", "label": "AI Agent Executor"}], "source_workflow_id": "490cfb2a-7b24-4d0a-95a8-8367d4abf191", "source_version_id": "3a40c317-9bc3-4ce8-b11d-160ee4696880", "has_updates": false, "current_version_id": "3a40c317-9bc3-4ce8-b11d-160ee4696880"}}