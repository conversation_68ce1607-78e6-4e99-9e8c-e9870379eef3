{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "aabd047c-7968-4c7c-a335-3d5502845751", "name": "Add Candidate", "description": "Add_Candidate", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/c2eea369-a526-4482-879f-086a97c44d99.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/9c8a0722-d9ca-45ac-b268-e100cce7e6f0.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753351326708"}, {"field": "row", "type": "array", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753351326708"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:57.236291", "updated_at": "2025-09-01T12:56:59.806921", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_add_single_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753351326708", "type": "mcp", "display_name": "Google Sheets - add_single_row", "label": "Google Sheets - add_single_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "487c7342-b590-4810-8974-e51ca613308c", "source_version_id": "17bc2837-d8ff-4dd4-864c-cf0002db3030", "has_updates": false, "current_version_id": "17bc2837-d8ff-4dd4-864c-cf0002db3030"}}