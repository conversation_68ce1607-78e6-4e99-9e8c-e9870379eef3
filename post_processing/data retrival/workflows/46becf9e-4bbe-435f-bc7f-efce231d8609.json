{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "46becf9e-4bbe-435f-bc7f-efce231d8609", "name": "Update Comapany Details", "description": "Update_Comapany_Details", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e0343446-dce4-4f24-8474-149035db522c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/130e0314-5133-4399-ba0b-807afd7fba3c.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753341145105"}, {"field": "values", "type": "array", "transition_id": "transition-MCP_Google_Sheets_update_row-1753688390368"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:10:06.359068", "updated_at": "2025-08-22T04:49:50.376310", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753341145105", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753684806104", "label": "Select Data"}, {"name": "MCP_Google_Sheets_update_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_row-1753688390368", "type": "mcp", "display_name": "Google Sheets - update_row", "label": "Google Sheets - update_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row_index": {"title": "Row Index", "type": "integer"}, "values": {"items": {"type": "string"}, "title": "Values", "type": "array"}}, "required": ["spreadsheet_id", "row_index", "values"], "title": "UpdateRow", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "9a374ea0-ff62-446a-a549-5b6bb1a61044", "source_version_id": "adcf47e8-10e1-42e2-8019-47b686355f6e", "has_updates": false, "current_version_id": "adcf47e8-10e1-42e2-8019-47b686355f6e"}}