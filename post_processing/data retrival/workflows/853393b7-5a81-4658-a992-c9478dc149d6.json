{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "853393b7-5a81-4658-a992-c9478dc149d6", "name": "Blogs Finder V1", "description": "Blogs_Finder_V1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/7d8ab514-6a56-4d99-aa0d-d287d68580bd.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cecdaf3d-d173-43aa-be4b-fcb2060f0d9c.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T09:51:57.052648", "updated_at": "2025-08-22T04:52:50.537357", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753422353677", "label": "Merge Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753423576004", "label": "Select Data"}, {"name": "ConditionalNode", "display_name": "Switch-Case Router", "type": "component", "transition_id": "transition-ConditionalNode-1753439292083", "label": "Switch-Case Router"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753427545491", "label": "Merge Data Condition 2"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753423749671", "label": "Merge Data Condition 1"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753427589329", "label": "Select Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753422401011", "label": "Select Data"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753422206896", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_get_values_in_range", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_get_values_in_range-1753423079288", "type": "mcp", "display_name": "Google Sheets - get_values_in_range", "label": "Google Sheets - get_values_in_range", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "range_notation": {"title": "Range Notation", "type": "string"}, "value_render_option": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "FORMATTED_VALUE", "title": "Value Render Option"}}, "required": ["spreadsheet_id", "range_notation"], "title": "GetValuesInRange", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "275f07e2-5c33-48ca-a567-c36a1b60d441", "source_version_id": "b53c7219-d9c9-46f6-b2fd-af1e3a18918b", "has_updates": false, "current_version_id": "b53c7219-d9c9-46f6-b2fd-af1e3a18918b"}}