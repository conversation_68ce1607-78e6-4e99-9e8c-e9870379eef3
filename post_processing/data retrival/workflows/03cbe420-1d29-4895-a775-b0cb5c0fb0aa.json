{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "03cbe420-1d29-4895-a775-b0cb5c0fb0aa", "name": "Blog Generation v5.1", "description": "Blog_Generation_v5.1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/14d67a94-4a1c-4897-a1d9-bcd76924bc98.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/6deaa3cb-e993-4e83-b6de-5b36b4ed182e.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753340852138"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753341788889"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-24T12:17:30.945366", "updated_at": "2025-08-22T04:53:52.137711", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753068818880", "label": "Object Converter"}, {"name": "IDGeneratorComponent", "display_name": "ID Generator", "type": "component", "transition_id": "transition-IDGeneratorComponent-1753340852138", "label": "BlogSummary ID Generator"}, {"name": "IDGeneratorComponent", "display_name": "ID Generator", "type": "component", "transition_id": "transition-IDGeneratorComponent-1753341788889", "label": "Blog Hash ID Generator"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753341458638", "label": "Combine Text"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753103897389", "label": "Blog Finder"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753341828764", "label": "Combine Text"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753093329990", "label": "Universal Converter"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104031847", "label": "TOC Agent"}, {"name": "MCP_Redis-MCP_set", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_set-1752828975549", "type": "mcp", "display_name": "Redis-MCP - set", "label": "Redis-MCP - set - Blog Research Summary", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"format": "binary", "type": "string"}, {"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expiration": {"default": null, "title": "Expiration", "type": "integer"}}, "required": ["key", "value"], "title": "setArguments", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-1Tee-NyeyVTqdovgMJBkP", "label": "Universal Converter - TOC"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753340715550", "label": "Combine For Loop Content Generation Results"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104311317", "label": "Content Agent"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752829183239", "label": "Combine Text"}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842677033", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Redis-MCP - hset - Toc", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752843178286", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Redis-MCP - hset - content", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104451500", "label": "Content Limiter Agent"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104617118", "label": "TOC Html Converter Agent"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752836825513", "label": "Merge Data"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104528822", "label": "HTML Converter"}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842792434", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Redis-MCP - hset - Blog TOC", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753181018607", "label": "Link Embedder Agent"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752836925668", "label": "Select Data"}, {"name": "MCP_Redis-MCP_hget", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hget-1752843332963", "type": "mcp", "display_name": "Redis-MCP - hget", "label": "Redis-MCP - hget - TOC", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}}, "required": ["name", "key"], "title": "hgetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104712427", "label": "Banner Image Prompt Gen"}, {"name": "MCP_Leonardo_MCP_generateHeroImage", "id": "19186c60-48de-4a75-9e39-ff830b76389f", "transition_id": "transition-MCP_Leonardo_MCP_generateHeroImage-1752836239946", "type": "mcp", "display_name": "Leonardo MCP - generateHeroImage", "label": "Leonardo MCP - generateHeroImage", "data": {"input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "Text to generate image from"}}, "required": ["prompt"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}}}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842412280", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Redis-MCP - hset - Setting Image", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752845441405", "label": "Merge After Setting Image"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753094271947", "label": "Select Data"}, {"name": "MCP_Redis-MCP_get", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_get-1752835998241", "type": "mcp", "display_name": "Redis-MCP - get", "label": "Redis-MCP - get - BlogSummary", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "getArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104847007", "label": "Fields Gen"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752845682015", "label": "Merge Data After Fields Generation"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752845795513", "label": "Universal Converter After Fields Generation"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752845885871", "label": "Select Data After Merging Fields Generation"}, {"name": "MC<PERSON>_<PERSON><PERSON>-MC<PERSON>_h<PERSON>all", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hgetall-1752845984651", "type": "mcp", "display_name": "Redis-MCP - hgetall", "label": "Redis-MCP - hgetall - After Field Generation", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}}, "required": ["name"], "title": "hgetallArguments", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752846019481", "label": "Merge Data After Getting Hash From Redis"}, {"name": "MCP_CMS-MCP_create_webflow_blog_post", "id": "fedc9765-3c28-491c-93f0-abb2e55e3161", "transition_id": "transition-MCP_CMS-MCP_create_webflow_blog_post-1753070748615", "type": "mcp", "display_name": "CMS-MCP - create_webflow_blog_post", "label": "CMS-MCP - create_webflow_blog_post", "data": {"input_schema": {"type": "object", "properties": {"webflowToken": {"type": "string", "description": "Webflow API token"}, "siteId": {"type": "string", "description": "Webflow site ID"}, "collectionName": {"type": "string", "description": "Name of the blog collection"}, "blogPost": {"type": "object", "properties": {"title": {"type": "string", "description": "Blog post title"}, "slug": {"type": "string", "description": "Blog post slug"}, "content": {"type": "string", "description": "Blog post content (HTML)"}, "excerpt": {"type": "string", "description": "Blog post excerpt"}, "publishDate": {"type": "string", "description": "Publish date (ISO format)"}, "author": {"type": "string", "description": "Author name"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Blog post tags"}, "featuredImage": {"type": "string", "description": "Featured image URL"}, "customFields": {"type": "object", "additionalProperties": {}, "description": "Additional custom field values"}}, "required": ["title", "slug", "content"], "additionalProperties": false, "description": "Blog post data"}, "publishAfterCreation": {"type": "boolean", "default": false, "description": "Whether to publish the site after creating the post"}, "customDomains": {"type": "array", "items": {"type": "string"}, "description": "Custom domains to publish to"}}, "required": ["webflowToken", "siteId", "collectionName", "blogPost"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753350661335", "label": "Merging Data After Publishing"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753350873057", "label": "Select Blog Summary Key Before Removing From Redis"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753350883988", "label": "Select Blog Gen Key Before Removing From Redis"}, {"name": "MCP_Redis_MCP_delete", "id": "6235c54e-3076-4118-9e2b-7ae3beb63229", "transition_id": "transition-MCP_Redis_MCP_delete-1753351381395", "type": "mcp", "display_name": "Redis MCP - delete", "label": "Redis MCP - delete  - BlogGen Hash", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "deleteArguments", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Redis_MCP_delete", "id": "6235c54e-3076-4118-9e2b-7ae3beb63229", "transition_id": "transition-MCP_Redis_MCP_delete-1753351331468", "type": "mcp", "display_name": "Redis MCP - delete", "label": "Redis MCP - delete - Blog Summary", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "deleteArguments", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "ce87e501-ef5b-4b2f-ad50-5c912114a2d6", "source_version_id": "218150ea-1c32-45db-b2e2-75346c618dba", "has_updates": false, "current_version_id": "218150ea-1c32-45db-b2e2-75346c618dba"}}