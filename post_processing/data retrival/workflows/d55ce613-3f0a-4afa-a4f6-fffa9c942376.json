{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "d55ce613-3f0a-4afa-a4f6-fffa9c942376", "name": "Blog Generation v5.5 - published", "description": "No description provided.", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5bac6de3-7ef0-42a4-8c92-98a11ee87f85.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/37ac2ced-178c-4fb7-9d6f-d7446f17fb53.json", "start_nodes": null, "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 8, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-01T11:33:27.077385", "updated_at": "2025-08-26T07:02:44.312067", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753068818880", "label": "Processing the user input"}, {"name": "IDGeneratorComponent", "display_name": "ID Generator", "type": "component", "transition_id": "transition-IDGeneratorComponent-1753341788889", "label": "Generating a hash-based unique ID for the blog"}, {"name": "IDGeneratorComponent", "display_name": "ID Generator", "type": "component", "transition_id": "transition-IDGeneratorComponent-1753340852138", "label": "Generating unique identifier for the blog summary"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753341828764", "label": "Combining ID with _BlogGen text"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753103897389", "label": "Finding the relevant blog content"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753341458638", "label": "Combining ID with _BlogResearchSummary text"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104031847", "label": "Generating the Table of Contents for the blog"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753093329990", "label": "Converting retrieved top blogs into a standard format for processing"}, {"name": "MCP_Redis-MCP_set", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_set-1752828975549", "type": "mcp", "display_name": "Redis-MCP - set", "label": "Storing Blog Research Summary in Redis", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"format": "binary", "type": "string"}, {"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expiration": {"default": null, "title": "Expiration", "type": "integer"}}, "required": ["key", "value"], "title": "setArguments", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-1Tee-NyeyVTqdovgMJBkP", "label": "Converting Table of Contents into a standard format"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104311317", "label": "Generating the main blog content for section"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753362499442", "label": "Converting iterated outputs into a unified format"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752829183239", "label": "Combining formatted Table of Contents with additional text"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104451500", "label": "Limiting the blog content to the required length"}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842677033", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Storing Table of Contents in Redis", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104617118", "label": "Converting Table of Contents into HTML format"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753340715550", "label": "Combining content generated from each iteration"}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842792434", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Storing Blog TOC HTML", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752843178286", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Storing final blog content", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104528822", "label": "Converting content into HTML format"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753181018607", "label": "Embedding relevant links into the content"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752836825513", "label": "Merging multiple data sources into a single structure"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752836925668", "label": "Selecting the required data from the merged result"}, {"name": "MCP_Redis-MCP_hget", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hget-1752843332963", "type": "mcp", "display_name": "Redis-MCP - hget", "label": "Retrieving Table of Contents for processing", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}}, "required": ["name", "key"], "title": "hgetArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104712427", "label": "Generating prompt for creating the banner image"}, {"name": "MCP_Leonardo_MCP_generateHeroImage", "id": "19186c60-48de-4a75-9e39-ff830b76389f", "transition_id": "transition-MCP_Leonardo_MCP_generateHeroImage-1752836239946", "type": "mcp", "display_name": "Leonardo MCP - generateHeroImage", "label": "Generating banner image", "data": {"input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "Text to generate image from"}}, "required": ["prompt"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}}}, {"name": "MCP_Redis-MCP_hset", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hset-1752842412280", "type": "mcp", "display_name": "Redis-MCP - hset", "label": "Storing blog image url", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expire_seconds": {"default": null, "title": "Expire Seconds", "type": "integer"}}, "required": ["name", "key", "value"], "title": "hsetArguments", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752845441405", "label": "Merging data after storing the image information"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753094271947", "label": "Extracting the required data from the merged result"}, {"name": "MCP_Redis-MCP_get", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_get-1752835998241", "type": "mcp", "display_name": "Redis-MCP - get", "label": "Retrieving Blog Summary for processing", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "getArguments", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753104847007", "label": "Generating metadata fields for the blog"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752845682015", "label": "Merging data after generating metadata fields"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752845795513", "label": "Converting metadata fields into a standard format after generation"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752845885871", "label": "Selecting necessary data after merging metadata fields"}, {"name": "MC<PERSON>_<PERSON><PERSON>-MC<PERSON>_h<PERSON>all", "id": "aa60d55c-c67a-4b05-8958-0af2dfb1c7a6", "transition_id": "transition-MCP_Redis-MCP_hgetall-1752845984651", "type": "mcp", "display_name": "Redis-MCP - hgetall", "label": "Retrieving all fields from database", "data": {"input_schema": {"properties": {"name": {"title": "Name", "type": "string"}}, "required": ["name"], "title": "hgetallArguments", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753361935184", "label": "Selecting content from the data retrieved"}, {"name": "SplitTextComponent", "display_name": "Split Text", "type": "component", "transition_id": "transition-SplitTextComponent-1753362004499", "label": "Splitting content  for post processing"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753362056164", "label": "Creating a content object from the processed data"}, {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "type": "component", "transition_id": "transition-AlterMetadataComponent-1753362188127", "label": "Updating object keys to modify metadata"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752846019481", "label": "Merging data after retrieving all data"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753350661335", "label": "Merging data after publishing is complete"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753788499230", "label": "Converting data into a unified format"}, {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "type": "component", "transition_id": "transition-AlterMetadataComponent-1753793195846", "label": "Modifying metadata fields"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753350873057", "label": "Selecting the Blog Summary key before deleting it"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753350883988", "label": "Selecting the Blog Generation key before deleting it"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753793274595", "label": "Selecting HTML content before publishing"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753793334428", "label": "Selecting the title from the data"}, {"name": "MCP_Redis_MCP_delete", "id": "6235c54e-3076-4118-9e2b-7ae3beb63229", "transition_id": "transition-MCP_Redis_MCP_delete-1753351331468", "type": "mcp", "display_name": "Redis MCP - delete", "label": "Deleting Blog Summary from temporary storage", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "deleteArguments", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-Y96lR6GBBgsczg-Wb2Ryw", "label": "Selecting the publish date from the data"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753794315524", "label": "Converting modified metadata into a standard format"}, {"name": "MCP_Redis_MCP_delete", "id": "6235c54e-3076-4118-9e2b-7ae3beb63229", "transition_id": "transition-MCP_Redis_MCP_delete-1753351381395", "type": "mcp", "display_name": "Redis MCP - delete", "label": "Deleting BlogGen hash from temporary storage", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "deleteArguments", "type": "object"}, "output_schema": {}}}, {"name": "IDGeneratorComponent", "display_name": "ID Generator", "type": "component", "transition_id": "transition-IDGeneratorComponent-1753793590924", "label": "Generating a unique identifier for saving blog data"}, {"name": "MCP_Google_Document_create_document", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "transition_id": "transition-MCP_Google_Document_create_document-1753793558400", "type": "mcp", "display_name": "Google Document - create_document", "label": "Creating a new Google Document", "data": {"input_schema": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "title": "CreateDocument", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753793642839", "label": "Extracting document ID from the Google Docs"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753793759006", "label": "Selecting the URL from the data"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753794156112", "label": "Merging text strings together"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753795841532", "label": "Merging document ID with HTML content"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753795945162", "label": "Selecting the document ID"}, {"name": "SplitTextComponent", "display_name": "Split Text", "type": "component", "transition_id": "transition-SplitTextComponent-1753794491947", "label": "Splitting text for further processing"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-kP5n9B2Xed-7ZugBJoi6r", "label": "Selecting the array of HTML content"}, {"name": "MCP_Google_Sheets_add_single_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753794600772", "type": "mcp", "display_name": "Google Sheets - add_single_row", "label": "Adding a single row to Google Sheets", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Document_append_document", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "transition_id": "transition-MCP_Google_Document_append_document-1753794061545", "type": "mcp", "display_name": "Google Document - append_document", "label": "Appending content to the Google Document", "data": {"input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753794110697", "label": "Converting data to a standard format and extracting the output"}], "source_workflow_id": "5c8bdefa-139b-4bb4-b560-d88b203823c7", "source_version_id": "f98d0402-e496-4172-bc90-f1592a5612c8", "has_updates": false, "current_version_id": "f98d0402-e496-4172-bc90-f1592a5612c8"}}