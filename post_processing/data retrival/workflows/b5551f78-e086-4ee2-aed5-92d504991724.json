{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "b5551f78-e086-4ee2-aed5-92d504991724", "name": "script_audio_generation", "description": "script_audio_generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/d122768b-e844-4696-ac5d-042cdd368d93.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/32b8cd2e-eaac-465d-8bb0-aac1345ab407.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_Script_Generation_script_generate-1750143976588"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 30, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T07:09:57.623074", "updated_at": "2025-08-25T20:53:51.204717", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Script_Generation_script_generate", "id": "47ee1ee5-5460-4806-9abd-1599613792cf", "transition_id": "transition-MCP_Script_Generation_script_generate-1750143976588", "type": "mcp", "display_name": "Script Generation - script_generate", "data": {"input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "title"}, "script": {"type": "string", "description": "The generated script", "title": "script"}, "script_type": {"type": "string", "description": "Type of the script", "title": "script_type"}, "video_type": {"type": "string", "description": "The type of video", "title": "video_type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "link"}}}}}, {"name": "MCP_Voice_generation_generate_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_generate_audio-1750143990001", "type": "mcp", "display_name": "Voice generation - generate_audio", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"properties": {"audio_ids": {"type": "array", "description": "List of generated audio IDs", "items": {"type": "string"}, "title": "audio_ids"}, "voice_id": {"type": "string", "description": "Identifier for the voice used in audio generation", "title": "voice_id"}, "audio_script": {"type": "string", "description": "<PERSON><PERSON><PERSON> used to generate the audio", "title": "audio_script"}}}}}, {"name": "MCP_Voice_generation_fetch_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_fetch_audio-1750752403861", "type": "mcp", "display_name": "Voice generation - fetch_audio", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"properties": {"audio_urls": {"type": "array", "items": {"type": "string"}, "description": "generated audio links", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "generated audio file mimetype", "title": "mimetype"}}}}}], "source_workflow_id": "8ae461ca-5393-47d2-afdc-6ce5e478d29d", "source_version_id": "ba92c89d-5056-4c7f-8c7e-13796e89ea94", "has_updates": false, "current_version_id": "ba92c89d-5056-4c7f-8c7e-13796e89ea94"}}