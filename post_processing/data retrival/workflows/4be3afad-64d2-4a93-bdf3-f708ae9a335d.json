{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "4be3afad-64d2-4a93-bdf3-f708ae9a335d", "name": "VC Automation - Email Generator Workflow - Sheet Based", "description": "VC_Automation_-_Email_Generator_Workflow_-_Sheet_Based", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/690a6f0a-ea1b-4727-9760-3a561e301a6c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cb077574-c6d0-4f72-84bd-ef21aee556c7.json", "start_nodes": [{"field": "spreadsheet_id", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1752247928534"}, {"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_get_cell-1752594639877"}], "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-14T05:36:01.607223", "updated_at": "2025-08-22T05:00:12.899771", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1752247928534", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Find first researched VC", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_get_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_get_cell-1752594639877", "type": "mcp", "display_name": "Google Sheets - get_cell", "label": "Get Company Context", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}}, "required": ["spreadsheet_id", "cell"], "title": "GetCell", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752255447169", "label": "Select Row Number"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752254185938", "label": "Select Rows Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752595025066", "label": "Select Company Context Value"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-YEXLXVwkA9tnYxAfD4ppz", "label": "Select Row Number"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-6qjRm0BNfjvs0Bn5CbZQ0", "label": "Select Research Data"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752255458041", "label": "Combine with G column"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-K3BbKMgpk1Bndk5ZrDco4", "label": "Combine with I column"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-40or4QDny-MZpGiK39vpk", "label": "Combine with F column"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-pRyA-o7qviDIpWAfhlkuS", "label": "Combine with J column"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-XRvyewi-vzKfBLDJDaTkP", "label": "Combine with H column"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752254310869", "label": "Select Name"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752596233512", "label": "Merge Data"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752255353670", "label": "Email 2"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752647302601", "label": "Email 1"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1752255615767", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update First Email Variation", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752327780364", "label": "Compose Data Email 1"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-T1jFEzd6ykmRFVhGPzbmo", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Second Email Variation", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-J3KjApXelaoG7bn16Xl-j", "label": "Compose Data Email 2"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-4otHOAzFOtSTah0ChUh_C", "label": "Humanize Email 2"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752327880512", "label": "Humanize Email 1"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-7vQ7qqm5Q8WC6igxm83ec", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Second Humanised Email ", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-A2K_2fMv1fjYAXTFfvWaK", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update First Humanised Email", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752507716293", "label": "Final Merge Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752507981224", "label": "Select Cell"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1752508047791", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update status cell", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "ce2a99cf-8ceb-498b-8bc7-3af603006c84", "source_version_id": "1fd305f5-48fd-4402-8714-7e66045dde16", "has_updates": false, "current_version_id": "1fd305f5-48fd-4402-8714-7e66045dde16"}}