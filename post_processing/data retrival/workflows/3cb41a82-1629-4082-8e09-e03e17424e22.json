{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "3cb41a82-1629-4082-8e09-e03e17424e22", "name": "Ciny_Video_generation", "description": "Ciny_Video_generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dc3a85ee-efa7-4e01-bb02-766deb4a34cb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a0d69af5-1980-4783-a5bc-9a6a6468950a.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_Script_Generation_script_generate-1750321066832"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 24, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-19T08:38:52.239472", "updated_at": "2025-08-22T05:01:59.102604", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Script_Generation_script_generate", "id": "47ee1ee5-5460-4806-9abd-1599613792cf", "transition_id": "transition-MCP_Script_Generation_script_generate-1750321066832", "type": "mcp", "display_name": "Script Generation - script_generate", "data": {"input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "title"}, "script": {"type": "string", "description": "The generated script", "title": "script"}, "script_type": {"type": "string", "description": "Type of the script", "title": "script_type"}, "video_type": {"type": "string", "description": "The type of video", "title": "video_type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "link"}}}}}, {"name": "MCP_Voice_generation_generate_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_generate_audio-1750321080079", "type": "mcp", "display_name": "Voice generation - generate_audio", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"properties": {"audio_ids": {"type": "array", "description": "List of generated audio IDs", "items": {"type": "string"}, "title": "audio_ids"}, "voice_id": {"type": "string", "description": "Identifier for the voice used in audio generation", "title": "voice_id"}, "audio_script": {"type": "string", "description": "<PERSON><PERSON><PERSON> used to generate the audio", "title": "audio_script"}}}}}, {"name": "MCP_Voice_generation_fetch_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_fetch_audio-1750321087513", "type": "mcp", "display_name": "Voice generation - fetch_audio", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"properties": {"audio_urls": {"type": "array", "items": {"type": "string"}, "description": "generated audio links", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "generated audio file mimetype", "title": "mimetype"}}}}}, {"name": "MCP_content-extractor-mcp_generate_subtitle", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "transition_id": "transition-MCP_content-extractor-mcp_generate_subtitle-1750321117029", "type": "mcp", "display_name": "content-extractor-mcp - generate_subtitle", "data": {"input_schema": {"properties": {"audio_urls": {"description": "List of audio URLs is required", "items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["audio_urls", "script"], "title": "GenerateSubtitle", "type": "object"}, "output_schema": {"properties": {"subtitle": {"type": "string", "description": "generated subtitle of the audio", "title": "subtitle"}}}}}, {"name": "MCP_Stock_Video_Generation_generate_stock_video", "id": "8f4dafe4-0682-4476-b342-b3ff1b9c98fa", "transition_id": "transition-MCP_Stock_Video_Generation_generate_stock_video-1750321356327", "type": "mcp", "display_name": "Stock Video Generation - generate_stock_video", "data": {"input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["script"], "title": "GenerateStockVideo", "type": "object"}, "output_schema": {"properties": {"stock_video_clips": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the video clip starts", "title": "at_time"}, "url": {"type": "string", "description": "URL of the stock video clip", "title": "url"}, "search_terms": {"type": "array", "items": {"type": "string"}, "description": "list of search terms", "title": "search_terms"}, "mimetype": {"type": "string", "description": "mimetype of the stock video clip", "title": "mimetype"}}}, "title": "stock_video_clips"}}}}}, {"name": "MCP_video-generation-mcp_generate_video", "id": "56dfe8af-e982-4351-a669-0a03755b8c99", "transition_id": "transition-MCP_video-generation-mcp_generate_video-1750321385510", "type": "mcp", "display_name": "video-generation-mcp - generate_video", "data": {"input_schema": {"$defs": {"EventStockClip": {"properties": {"clip": {"minimum": 0, "title": "Clip", "type": "integer"}, "at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "duration": {"exclusiveMinimum": 0, "title": "Duration", "type": "number"}}, "required": ["clip", "at_time", "duration"], "title": "EventStockClip", "type": "object"}, "StockImageClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockImageClip", "type": "object"}, "StockVideoClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockVideoClip", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "stock_video_clips": {"default": [], "items": {"$ref": "#/$defs/StockVideoClip"}, "title": "Stock Video Clips", "type": "array"}, "stock_image_clips": {"default": [], "items": {"$ref": "#/$defs/StockImageClip"}, "title": "Stock Image Clips", "type": "array"}, "event_stock_clips": {"default": [], "items": {"$ref": "#/$defs/EventStockClip"}, "title": "Event Stock Clips", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "avatar_video_urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Avatar Video Urls"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the thumbnail", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail", "title": "mimetype"}}, "title": "thumbnail"}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the video", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the video", "title": "mimetype"}}, "title": "video_link"}, "duration": {"type": "number", "description": "Duration of the video", "title": "duration"}}}}}, {"name": "MCP_Stock_Image_Generation_generate_ai_stock_image", "id": "3747169a-0142-4b36-9140-e1f40e576257", "transition_id": "transition-MCP_Stock_Image_Generation_generate_ai_stock_image-1751029929886", "type": "mcp", "display_name": "Stock Image Generation - generate_ai_stock_image", "data": {"input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "view_type": {"maxLength": 50, "minLength": 1, "title": "View Type", "type": "string"}}, "required": ["script", "view_type"], "title": "GenerateAIStockImage", "type": "object"}, "output_schema": {"type": "object", "properties": {"generated_stock_image": {"type": "array", "description": "the stock images generated", "title": "generated_stock_image"}}, "required": ["generated_stock_image"]}}}], "source_workflow_id": "375bd1de-79e6-4055-a143-f4f8d24c80c6", "source_version_id": "4f77a287-280b-4b47-8d6f-d8d3c6026f90", "has_updates": false, "current_version_id": "4f77a287-280b-4b47-8d6f-d8d3c6026f90"}}