{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "807c0057-3203-4199-af37-74e240547ada", "name": "Google Sheets Row Reader v1", "description": "Google_Sheets_Row_Reader_v1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/78e3eb66-3da0-4af7-825f-3aa15706ddf0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a60f8f72-ab60-4c70-b51a-a9618afee843.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T10:54:09.606373", "updated_at": "2025-08-22T04:52:12.049592", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753422353677", "label": "Merge Data After Receiving Input"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753422401011", "label": "Select blogId from input"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753422206896", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "b2d64032-06db-4cf0-9704-fc40e3ff2cbc", "source_version_id": "a3afc6c9-b4f0-4c89-9e62-a0f6b1246aff", "has_updates": false, "current_version_id": "a3afc6c9-b4f0-4c89-9e62-a0f6b1246aff"}}