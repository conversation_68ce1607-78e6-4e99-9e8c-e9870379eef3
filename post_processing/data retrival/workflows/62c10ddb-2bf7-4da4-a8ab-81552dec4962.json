{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "62c10ddb-2bf7-4da4-a8ab-81552dec4962", "name": "VC Automation - Research Workflow - Sheet Based", "description": "VC_Automation_-_Research_Workflow_-_Sheet_Based", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3556c5e9-b116-4605-af0b-bf1dcdc0f46c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3a63f48c-208f-4e0e-bebf-02f93b31a5f2.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1752120450511"}], "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-21T08:40:50.442307", "updated_at": "2025-08-22T04:55:46.268077", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1752120450511", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Find Row with <PERSON><PERSON>", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752233152902", "label": "Select Row Number"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752126209553", "label": "Select Row Data"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752245133042", "label": "Combine with F column"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752228953715", "label": "Select Linkedin Url"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752228336890", "label": "Select Name"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752233218870", "label": "Combine with E column"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752228718683", "label": "Select VC Firm"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752228644996", "label": "Merge Name, VC Firm and LinkedIn Url"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752230701584", "label": "Research - Personal Profile"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752227392622", "label": "Research - Recent Investments"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752230665802", "label": "Research - Portfolio Companies"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752230424590", "label": "Research - Funding Rounds"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752231045083", "label": "Combine Research Results"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1752233302625", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Research Data", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752472523956", "label": "Merge Final Output with Cell Number"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752472602724", "label": "Select only cell number"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1752245096665", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update status as Researched", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "12a4c3f3-d1b4-4eb4-82f7-0cb8209c93cd", "source_version_id": "a9af827b-56a7-43a7-a448-66000dbceaa0", "has_updates": false, "current_version_id": "a9af827b-56a7-43a7-a448-66000dbceaa0"}}