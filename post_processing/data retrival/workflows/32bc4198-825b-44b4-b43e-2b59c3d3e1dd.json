{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "32bc4198-825b-44b4-b43e-2b59c3d3e1dd", "name": "Ruh_Video_Generation", "description": "Ruh_Video_Generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/f7b13e6f-4bed-440d-be64-ebc04dc9431b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a5c22be3-a94e-4966-87f3-4e079e7c75a8.json", "start_nodes": [{"field": "script_type", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "tone", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "length", "type": "int", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "topic", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "characters", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "scene", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "cta", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "view_type", "type": "dropdown", "transition_id": "transition-MCP_cinematic-video-generator_generate_video-1754056525924"}], "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.9.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-27T13:19:26.398759", "updated_at": "2025-08-22T05:01:51.151149", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_script-generation-mcp-server_generate_script", "id": "ce1e8436-63b7-4f64-86b0-3cecc3bbd05b", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017", "type": "mcp", "display_name": "script-generation-mcp-server - generate_script", "label": "script-generation-mcp-server - generate_script", "data": {"input_schema": {"properties": {"script_type": {"description": "Type of script to generate (e.g., 'Ad', 'Story', etc.)", "title": "Script Type", "type": "string"}, "tone": {"description": "Tone of the script (e.g., 'Professional', 'Casual', etc.)", "title": "<PERSON><PERSON>", "type": "string"}, "length": {"anyOf": [{"type": "integer"}, {"type": "string"}], "description": "Total script duration in seconds. Can be an integer or a string containing only digits.", "title": "Length"}, "topic": {"description": "Main Topic of the script.", "title": "Topic", "type": "string"}, "characters": {"description": "Characters to include. Enter 'nil' to opt out.", "title": "Characters", "type": "string"}, "scene": {"description": "Specific scene description to be included in the script. Enter 'nil' to opt out.", "title": "Scene", "type": "string"}, "cta": {"description": "Call-to-action line. Enter 'nil' to opt out.", "title": "Cta", "type": "string"}, "scene_duration": {"anyOf": [{"type": "integer"}, {"type": "string"}, {"type": "null"}], "default": null, "description": "Duration in seconds for each scene/part. Can be an integer or a string containing only digits. If equal to length, only one part will be generated. Optional.", "title": "Scene Duration"}}, "required": ["script_type", "tone", "length", "topic", "characters", "scene", "cta"], "title": "ScriptInput", "type": "object"}, "output_schema": {"properties": {"script": {"type": "array", "description": "A list of scene objects, each with a scene label and its cinematic visual description. Contains only visual elements without any sound or audio descriptions.", "title": "script", "items": {"type": "object", "description": "A scene object with a single key (scene label) and its visual content as the value."}}, "audio_text": {"type": "string", "description": "The voiceover-only text for audio generation.", "title": "audio_text"}, "sound_effects": {"type": "array", "description": "A list of scene objects, each with a scene label and its detailed sound effects including ambient sounds, action sounds, and audio cues.", "title": "sound_effects", "items": {"type": "object", "description": "A scene object with a single key (scene label) and its sound effects as the value."}}, "title": {"type": "string", "description": "A concise title for the script, maximum 6-7 words.", "title": "title"}, "error": {"type": "string", "description": "Error message if script generation failed.", "title": "error"}}}}}, {"name": "MCP_voice-generation-mcp_generate_audio", "id": "068600be-4d02-4c06-a7f1-513d060cbfab", "transition_id": "transition-NPKgMbfWNpQ5qcBsuqz3P", "type": "mcp", "display_name": "voice-generation-mcp - generate_audio", "label": "voice-generation-mcp - generate_audio (Copy)", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "audio ids received from Eleven labs", "title": "audio_ids"}, "voice_id": {"type": "string", "description": "voice id", "title": "voice_id"}, "audio_script": {"type": "string", "description": "audio script", "title": "audio_script"}}, "required": ["audio_ids", "voice_id", "audio_script"]}}}, {"name": "MCP_voice-generation-mcp_fetch_audio", "id": "068600be-4d02-4c06-a7f1-513d060cbfab", "transition_id": "transition-mlQvwTPgohB7xtyZGZfO5", "type": "mcp", "display_name": "voice-generation-mcp - fetch_audio", "label": "voice-generation-mcp - fetch_audio (Copy)", "data": {"input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "Urls of the Audio", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "Mimetype of the audio", "title": "mimetype"}}, "required": ["audio_urls", "mimetype"]}}}, {"name": "DelayComponent", "display_name": "Wait / Delay", "type": "component", "transition_id": "transition-LbI1ZJfz-wzdRJT3u5tHI", "label": "Wait / Delay "}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-2ClYXkERc6gjlB0cq_diF", "label": "VisionCrafter"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-2c7aXYy_6NePREbWYzQsg", "label": "Final Frame Extractor"}, {"name": "ApiRequestNode", "display_name": "API Request", "type": "component", "transition_id": "transition-CjMjGrR84MSNbPjecw7Wg", "label": "Image Generation Process"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-HkC41fRvQdtGnzirBnbIa", "label": "Universal Converter "}, {"name": "MCP_content-extractor-mcp_generate_subtitle", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "transition_id": "transition-J46pOqMIfefpLUexpHpfB", "type": "mcp", "display_name": "content-extractor-mcp - generate_subtitle", "label": "content-extractor-mcp - generate_subtitle (Copy)", "data": {"input_schema": {"properties": {"audio_urls": {"description": "List of audio URLs is required", "items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["audio_urls", "script"], "title": "GenerateSubtitle", "type": "object"}, "output_schema": {"properties": {"subtitle": {"type": "string", "description": "generated subtitle of the audio", "title": "subtitle"}}}}}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-nnNKYXY_vb79m_2tugtkZ", "label": "Combine Text"}, {"name": "MCP_cinematic-video-generator_generate_video", "id": "a715b48e-832c-4698-99cd-8906fed43bb3", "transition_id": "transition-MCP_cinematic-video-generator_generate_video-1754056525924", "type": "mcp", "display_name": "cinematic-video-generator - generate_video", "label": "cinematic-video-generator - generate_video", "data": {"input_schema": {"$defs": {"Scene": {"properties": {"start_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Start Image"}, "video": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Video"}, "end_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "End Image"}}, "title": "Scene", "type": "object"}, "ScriptScene": {"additionalProperties": {"type": "string"}, "title": "ScriptScene", "type": "object"}, "SoundEffect": {"additionalProperties": {"items": {"type": "string"}, "type": "array"}, "title": "SoundEffect", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "scenes": {"default": [], "items": {"$ref": "#/$defs/Scene"}, "title": "Scenes", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}, "title": {"title": "Title", "type": "string"}, "sound_effects": {"default": [], "items": {"$ref": "#/$defs/SoundEffect"}, "title": "Sound Effects", "type": "array"}, "script": {"default": [], "items": {"$ref": "#/$defs/ScriptScene"}, "title": "<PERSON><PERSON><PERSON>", "type": "array"}}, "required": ["view_type", "audio_urls", "subtitles", "title"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"type": "object", "properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "mimetype": {"type": "string"}}}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "mimetype": {"type": "string"}}}, "duration": {"type": "number"}}}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-RF2JjuMjL08GLEBUa9WAv", "label": "cinematic video prompt generator "}, {"name": "ApiRequestNode", "display_name": "API Request", "type": "component", "transition_id": "transition-ApiRequestNode-1752498081790", "label": "Create Video"}, {"name": "DelayComponent", "display_name": "Wait / Delay", "type": "component", "transition_id": "transition-DelayComponent-1752515319476", "label": "Wait / Delay"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752516543370", "label": "Select url for status check"}, {"name": "ApiRequestNode", "display_name": "API Request", "type": "component", "transition_id": "transition-9FV5wyOEbinE3GzzevP-P", "label": "Check Video Status"}], "source_workflow_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a", "source_version_id": "1e868c07-b1dd-4895-add9-c6a3646826d6", "has_updates": false, "current_version_id": "1e868c07-b1dd-4895-add9-c6a3646826d6"}}