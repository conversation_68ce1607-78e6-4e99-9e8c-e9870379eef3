{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "6ef72d4e-1dd6-4fa9-ac31-33ffe4a85909", "name": "Candidate_api_request", "description": "Candidate_api_request", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/79b4d632-a459-4d26-b1b4-7119318ead65.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/27710853-0dea-4fda-8fe3-5c499d111f84.json", "start_nodes": [{"field": "resume_s3_link", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750142649253"}, {"field": "job_description_s3_link", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750142649253"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T07:13:32.854915", "updated_at": "2025-08-25T12:07:39.806120", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Candidate_Interview_candidate_suitability_pre", "id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "type": "mcp", "data": {"display_name": "Candidate_Interview - candidate_suitability_pre", "input_schema": {"properties": {"resume_s3_link": {"description": "S3 link to the candidate's resume", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Resume S3 Link", "type": "string"}, "job_description_s3_link": {"description": "S3 link to the job description", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Job Description S3 Link", "type": "string"}}, "required": ["resume_s3_link", "job_description_s3_link"], "title": "CandidateSuitabilityPreSchema", "type": "object"}, "output_schema": {"properties": {"suitability_analysis": {"type": "string", "description": "Analysis of candidate's suitability for the job", "title": "suitability_analysis"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}, {"name": "ApiRequestNode", "display_name": "API Request", "type": "component"}], "source_workflow_id": "17636093-54e1-4f21-a1c9-53c4c3f83db7", "source_version_id": "1f1300e5-3122-4716-a5d5-0c08c84cf50b", "has_updates": false, "current_version_id": "1f1300e5-3122-4716-a5d5-0c08c84cf50b"}}