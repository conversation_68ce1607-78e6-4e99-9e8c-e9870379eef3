{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "18d5a6b9-c0e1-4e79-9300-daec6bf6e13a", "name": "Blog Gen 1", "description": "Blog_Gen_1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/2a601a80-fe7d-4077-8c90-2fb877b80bf0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/2f717d91-830f-47c1-ae72-f74411c9260b.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1752749761139"}], "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.10.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-02T14:46:41.864509", "updated_at": "2025-08-22T05:00:37.027771", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752749761139", "label": "Universal Converter"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1752752231600", "label": "Combine Text"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-xpJiNyTYF6rvSgIfdlx9Q", "label": "Universal Converter (Copy)"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751370463389", "label": "Blogs Finder"}, {"name": "MCP_Redis-mcp-01_set", "id": "056ba66b-6222-4e7a-adb9-186193075886", "transition_id": "transition-MCP_Redis-mcp-01_set-1752749866470", "type": "mcp", "display_name": "Redis-mcp-01 - set", "label": "Redis-mcp-01 - set", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}, "value": {"anyOf": [{"format": "binary", "type": "string"}, {"type": "string"}, {"type": "integer"}, {"type": "number"}], "title": "Value"}, "expiration": {"default": null, "title": "Expiration", "type": "integer"}}, "required": ["key", "value"], "title": "setArguments", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1751463692126", "label": "To Array"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752061045527", "label": "Diagram + Original Data - Merge Data"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752754078807", "label": "Merge Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-osl6tMtpK_xNIcxkMAEmp", "label": "Aggregat data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752754828929", "label": "Select Data"}, {"name": "MCP_Redis-mcp-01_get", "id": "056ba66b-6222-4e7a-adb9-186193075886", "transition_id": "transition-MCP_Redis-mcp-01_get-1752750397463", "type": "mcp", "display_name": "Redis-mcp-01 - get", "label": "Redis-mcp-01 - get", "data": {"input_schema": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "getArguments", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752755178956", "label": "Universal Converter"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752135089640", "label": "Aggregate-Content"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752149733634", "label": "JSON string to Object"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752149748667", "label": "Select content from summary"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-EXbxrpCvNgS1L7LtX3yY-", "label": "Universal Converter (Copy)"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752580296683", "label": "Image Prompt generator"}, {"name": "MCP_Leonardo_AI_Image_Generator_generateHeroImage", "id": "52788dad-42a9-4e03-afe2-1ac310db76b1", "transition_id": "transition-MC<PERSON>_Leonardo_AI_Image_Generator_generateHeroImage-1752149702322", "type": "mcp", "display_name": "Leonardo AI Image Generator - generateHeroImage", "label": "Leonardo AI Image Generator - generateHeroImage", "data": {"input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "Text to generate image from"}}, "required": ["prompt"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1752480135803", "label": "banner_image in dict"}, {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "type": "component", "transition_id": "transition-AlterMetadataComponent-1752479607254", "label": "Add Banner_image in original data"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1752488759596", "label": "Universal Converter"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1752836170538", "label": "topics array"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752486685768", "label": "HTML generator"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-JwoUM08w105PENMqGzLm2", "label": "Universal Converter (Copy)"}], "source_workflow_id": "88a79cc0-9c2f-4bd7-8dba-0ee1b290e11e", "source_version_id": "424e1c12-0d19-4625-8869-9e27746eb550", "has_updates": false, "current_version_id": "424e1c12-0d19-4625-8869-9e27746eb550"}}