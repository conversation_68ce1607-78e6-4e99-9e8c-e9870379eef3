#!/usr/bin/env python3
"""
Python client for testing the SSE workflow generation endpoint.
Demonstrates how to consume Server-Sent Events from the API.
"""

import json
import requests
import time
from typing import Dict, Any, Generator


def parse_sse_stream(response: requests.Response) -> Generator[Dict[str, Any], None, None]:
    """
    Parse Server-Sent Events from a streaming response.
    
    Args:
        response: requests.Response object with streaming enabled
        
    Yields:
        Dict containing event_type and data for each SSE event
    """
    event_type = None
    event_data = None
    
    for line in response.iter_lines(decode_unicode=True):
        if line is None:
            continue
            
        line = line.strip()
        
        if line.startswith('event: '):
            event_type = line[7:].strip()
        elif line.startswith('data: '):
            event_data = line[6:].strip()
        elif line == '' and event_type and event_data:
            # Complete event received
            try:
                data = json.loads(event_data)
                yield {
                    'event_type': event_type,
                    'data': data
                }
            except json.JSONDecodeError as e:
                print(f"Failed to parse event data: {e}")
                print(f"Raw data: {event_data}")
            
            # Reset for next event
            event_type = None
            event_data = None


def test_sse_endpoint(base_url: str = "http://127.0.0.1:8000", task: str = None):
    """
    Test the SSE workflow generation endpoint.
    
    Args:
        base_url: Base URL of the API server
        task: Task description for workflow generation
    """
    if task is None:
        task = ("Craft a workflow to create a short video script from a given topic, "
                "with a specific duration of one minute. Use this script to generate "
                "audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, "
                "and then fetch the generated audio file.")
    
    url = f"{base_url}/api/v1/generate_workflow/stream"
    payload = {"task": task}
    
    print(f"🚀 Starting SSE test for: {task[:100]}...")
    print(f"📡 Connecting to: {url}")
    print("=" * 80)
    
    try:
        # Make streaming request
        response = requests.post(
            url,
            json=payload,
            stream=True,
            headers={
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            },
            timeout=300  # 5 minute timeout
        )
        
        response.raise_for_status()
        
        print("✅ Connected successfully!")
        print("📥 Receiving events...\n")
        
        event_count = 0
        start_time = time.time()
        
        # Process events
        for event in parse_sse_stream(response):
            event_count += 1
            event_type = event['event_type']
            data = event['data']
            
            # Format timestamp
            if 'timestamp' in data:
                elapsed = data['timestamp'] - start_time
                timestamp_str = f"[+{elapsed:.2f}s]"
            else:
                timestamp_str = f"[{time.strftime('%H:%M:%S')}]"
            
            # Print event with formatting
            print(f"📨 Event #{event_count} {timestamp_str} {event_type.upper()}")
            
            if event_type == 'start':
                print(f"   🎯 Task: {data.get('task', 'N/A')}")
                print(f"   💬 {data.get('message', 'N/A')}")
                
            elif event_type == 'processing':
                print(f"   ⚙️  {data.get('message', 'N/A')}")
                print(f"   📍 Stage: {data.get('stage', 'N/A')}")
                
            elif event_type == 'agent_complete':
                print(f"   🤖 Agent: {data.get('agent', 'N/A')}")
                print(f"   ✅ {data.get('message', 'N/A')}")
                
            elif event_type == 'post_processing':
                print(f"   🔄 {data.get('message', 'N/A')}")
                
            elif event_type == 'success':
                print(f"   🎉 {data.get('message', 'N/A')}")
                if 'workflow' in data:
                    workflow = data['workflow']
                    print(f"   📋 Workflow generated with {len(workflow.get('nodes', []))} nodes")
                    
            elif event_type == 'complete':
                print(f"   ✅ {data.get('message', 'N/A')}")
                
            elif event_type == 'error':
                print(f"   ❌ Error: {data.get('error', 'N/A')}")
                print(f"   💬 {data.get('message', 'N/A')}")
                
            elif event_type == 'done':
                print(f"   🏁 {data.get('message', 'N/A')}")
                if 'result' in data:
                    result = data['result']
                    if 'workflow' in result:
                        print(f"   📊 Final workflow: {len(result['workflow'].get('nodes', []))} nodes")
                    elif 'message' in result:
                        print(f"   📝 Final message: {result['message'][:100]}...")
                break
            
            print()  # Empty line for readability
        
        total_time = time.time() - start_time
        print("=" * 80)
        print(f"✅ Stream completed successfully!")
        print(f"📊 Total events received: {event_count}")
        print(f"⏱️  Total time: {total_time:.2f} seconds")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def test_regular_endpoint(base_url: str = "http://127.0.0.1:8000", task: str = None):
    """
    Test the regular (non-streaming) workflow generation endpoint for comparison.
    
    Args:
        base_url: Base URL of the API server
        task: Task description for workflow generation
    """
    if task is None:
        task = ("Craft a workflow to create a short video script from a given topic, "
                "with a specific duration of one minute.")
    
    url = f"{base_url}/api/v1/generate_workflow"
    payload = {"task": task}
    
    print(f"🔄 Testing regular endpoint: {url}")
    print(f"🎯 Task: {task[:100]}...")
    
    try:
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=300)
        response.raise_for_status()
        total_time = time.time() - start_time
        
        result = response.json()
        print(f"✅ Request completed in {total_time:.2f} seconds")
        
        if 'workflow' in result:
            workflow = result['workflow']
            print(f"📋 Workflow generated with {len(workflow.get('nodes', []))} nodes")
        elif 'message' in result:
            print(f"📝 Message: {result['message'][:200]}...")
        
        return result
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test SSE workflow generation endpoint")
    parser.add_argument("--url", default="http://127.0.0.1:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--task", 
                       help="Custom task description")
    parser.add_argument("--compare", action="store_true",
                       help="Also test the regular endpoint for comparison")
    
    args = parser.parse_args()
    
    if args.compare:
        print("🔍 Testing regular endpoint first...")
        test_regular_endpoint(args.url, args.task)
        print("\n" + "=" * 80 + "\n")
    
    print("🌊 Testing SSE streaming endpoint...")
    test_sse_endpoint(args.url, args.task)
