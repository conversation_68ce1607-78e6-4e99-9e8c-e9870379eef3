import json
import logging
from datetime import datetime, timezone


class JSONFormatter(logging.Formatter):
    def __init__(self, session_id):
        super().__init__()
        self.session_id = session_id

    def format(self, record):
        log_record = {
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
            "level": record.levelname,
            "session_id": self.session_id,
            "logger": record.name,
            "message": record.getMessage(),
        }
        return json.dumps(log_record)


def get_logger(session_id):
    logger = logging.getLogger("strands")
    logger.setLevel(logging.INFO)

    if not logger.handlers:  # avoid duplicate handlers
        file_handler = logging.FileHandler("strands.jsonl")
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(JSONFormatter(session_id))
        logger.addHandler(file_handler)

    return logger
