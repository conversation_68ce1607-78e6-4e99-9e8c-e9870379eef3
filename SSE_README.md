# Server-Sent Events (SSE) for Workflow Generation

This implementation adds real-time streaming capabilities to the workflow generation API using Server-Sent Events (SSE).

## Overview

The SSE endpoint provides real-time updates during the workflow generation process, allowing clients to:
- Monitor progress as the workflow is being generated
- Receive intermediate results from different agents
- Handle errors gracefully with immediate feedback
- Provide better user experience with live updates

## Endpoints

### 1. Streaming Endpoint
```
POST /api/v1/generate_workflow/stream
```

**Request Body:**
```json
{
  "task": "Your workflow task description here"
}
```

**Response:** Server-Sent Events stream with the following event types:

- `start` - Initial event when processing begins
- `processing` - When graph execution starts
- `agent_complete` - When each agent completes processing
- `post_processing` - When final processing begins
- `success` - When workflow is successfully generated
- `complete` - When processing completes with a message
- `error` - If an error occurs during processing
- `done` - Final event indicating completion

### 2. Regular Endpoint (unchanged)
```
POST /api/v1/generate_workflow
```

Returns the complete result after processing is finished.

## Event Format

Each SSE event follows this format:
```
event: <event_type>
data: <json_data>

```

Example events:
```
event: start
data: {"message": "Starting workflow generation", "task": "...", "timestamp": 1234567890.123}

event: agent_complete
data: {"agent": "router", "message": "Agent router completed processing", "timestamp": 1234567890.456}

event: success
data: {"message": "Workflow generated successfully", "workflow": {...}, "timestamp": 1234567890.789}

event: done
data: {"message": "Workflow generation completed", "result": {...}, "timestamp": 1234567890.999}
```

## Usage Examples

### JavaScript (Browser)
```javascript
// Using fetch with streaming
async function streamWorkflow(task) {
    const response = await fetch('/api/v1/generate_workflow/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ task: task })
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        // Parse SSE events from chunk
        parseSSEEvents(chunk);
    }
}
```

### Python
```python
import requests
import json

def stream_workflow(task):
    response = requests.post(
        'http://localhost:8000/api/v1/generate_workflow/stream',
        json={'task': task},
        stream=True
    )
    
    for line in response.iter_lines(decode_unicode=True):
        if line.startswith('event: '):
            event_type = line[7:].strip()
        elif line.startswith('data: '):
            event_data = json.loads(line[6:].strip())
            print(f"Event: {event_type}, Data: {event_data}")
```

### cURL
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"task": "Create a simple workflow"}' \
  http://localhost:8000/api/v1/generate_workflow/stream
```

## Test Clients

### HTML Test Client
Open `sse_test_client.html` in your browser to test the SSE endpoint with a visual interface.

### Python Test Client
Run the Python test client:
```bash
python sse_test_client.py --url http://localhost:8000 --task "Your task here"

# Compare with regular endpoint
python sse_test_client.py --compare
```

## Implementation Details

### Key Features
1. **Non-blocking**: Uses `asyncio.to_thread()` to run the graph execution without blocking the event loop
2. **Error Handling**: Comprehensive error handling with proper SSE error events
3. **CORS Support**: Includes CORS headers for browser compatibility
4. **Progress Tracking**: Provides detailed progress information at each step
5. **Timestamps**: All events include timestamps for timing analysis

### Headers
The SSE endpoint sets these important headers:
- `Content-Type: text/event-stream`
- `Cache-Control: no-cache`
- `Connection: keep-alive`
- `Access-Control-Allow-Origin: *` (for CORS)

### Performance Considerations
- The streaming endpoint uses the same underlying workflow generation logic
- Events are sent as soon as they're available, providing real-time feedback
- Memory usage is optimized by streaming events rather than buffering
- Connection timeouts should be configured appropriately for long-running workflows

## Error Handling

The SSE implementation includes robust error handling:
- JSON parsing errors are caught and reported
- Network errors are handled gracefully
- Workflow execution errors are streamed as error events
- Clients can detect and handle different error types

## Browser Compatibility

SSE is supported in all modern browsers:
- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- IE: Not supported (use polyfill if needed)

## Security Considerations

- The endpoint accepts POST requests to avoid URL length limitations
- CORS is enabled for development (configure appropriately for production)
- Consider adding authentication/authorization as needed
- Rate limiting should be implemented for production use

## Monitoring and Debugging

- All events include timestamps for performance analysis
- The regular endpoint remains available for comparison
- Detailed logging is maintained for debugging
- Test clients provide comprehensive event monitoring
