import json

from strands.multiagent import GraphBuilder


def extract_json_output(state):
    router_node = state.results["router"]
    output = str(router_node.result)
    if "```json" in output:
        output = output.split("```json")[1].split("```")[0]
    output = json.loads(output)
    return output


def _to_post_processing(state):
    message = extract_json_output(state)
    return message["next_agent"] == "post_processing"


def get_graph(agents):
    prompt_enhancement_agent = agents["prompt_enhancement"]
    post_processing_agent = agents["post_processing"]
    router_agent = agents["router"]

    builder = GraphBuilder()

    builder.add_node(prompt_enhancement_agent, "prompt_enhancement")
    builder.add_node(post_processing_agent, "post_processing")
    builder.add_node(router_agent, "router")

    builder.add_edge("prompt_enhancement", "router")
    builder.add_edge("router", "post_processing", condition=_to_post_processing)

    builder.set_entry_point("prompt_enhancement")

    return builder.build()
