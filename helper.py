import re
import json
import requests


def normalize_name(name: str) -> str:
    name = re.sub(r"[\s\-]+", "_", name)
    name = re.sub(r"_+", "_", name)
    name = name.strip("_")
    return name


def context_component(data):
    # url = "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    # response = requests.get(url)
    # response.raise_for_status()  # Raise error if request fails
    # components = response.json()
    with open("post_processing/data retrival/components.json", "r") as f:
        components = json.load(f)
    category = data.get("category")
    name_key = data.get("name")

    if not category or category not in components:
        return f"Category '{category}' not found."

    if not name_key or name_key not in components[category]:
        return f"Component '{name_key}' not found in category '{category}'."

    component = components[category][name_key]
    name = component.get("name", "N/A")
    description = component.get("description", "No description")
    inputs = component.get("inputs", [])
    outputs = component.get("outputs", [])

    context = f"Name : {name}\nDescription : {description}\nOriginalType : {name}\nType : Component\n"

    context += "Inputs :-\n"
    for i in inputs:
        context += f"Input Name : {i.get('name', 'N/A')}\n"
        context += f"Input Info : {i.get('info', '')}\n"
        context += f"Input Type : {i.get('input_type', '')}\n"
        input_types = i.get("input_types")
        if input_types:
            context += "Input Types : " + ", ".join(input_types) + "\n"
        if i.get("required"):
            context += "Required\n"
        if i.get("is_handle"):
            context += "Handle\n"
        if i.get("is_list"):
            context += "List\n"
        if i.get("real_time_refresh"):
            context += "Real Time Refresh\n"
        if i.get("advanced"):
            context += "Advanced\n"
        if i.get("value") is not None:
            context += f"Default Value : {i['value']}\n"
        options = i.get("options")
        if options:
            context += "Options : " + ", ".join(options) + "\n"
        context += "\n"

    context += "Outputs :-\n"
    for o in outputs:
        context += f"Output Name : {o.get('name', 'N/A')}\n"
        context += f"Output Type : {o.get('output_type', '')}\n"
        if o.get("semantic_type"):
            context += f"Semantic Type : {o['semantic_type']}\n"
        if o.get("method"):
            context += f"Method : {o['method']}\n"
        context += "\n"

    return context


def workflow_context(data):
    workflow_id = data.get("id")
    if not workflow_id:
        return "Workflow ID is missing."

    # url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{workflow_id}"
    # try:
    #     response = requests.get(url)
    #     response.raise_for_status()
    #     workflow_data = response.json()
    # except requests.RequestException as e:
    #     return f"Failed to fetch workflow: {e}"
    # except ValueError:
    #     return "Invalid JSON response from API."

    with open(f"post_processing/data retrival/workflows/{workflow_id}.json", "r") as f:
        workflow_data = json.load(f)

    workflow = workflow_data.get("workflow")
    if not workflow:
        return f"No workflow found for ID '{workflow_id}'."

    name = workflow.get("name", "N/A")
    description = workflow.get("description", "No description")
    inputs = workflow.get("start_nodes", [])

    outputs = [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]

    context = (
        f"Name : {name}\n"
        f"Description : {description}\n"
        f"OriginalType : workflow-{workflow_id}\n"
        f"Type : Workflow\n"
    )

    context += "Inputs :-\n"
    for i in inputs:
        field_name = i.get("field", "N/A")
        field_type = i.get("type", "N/A")
        context += f"Input Name : {field_name}\n"
        context += f"Input Info : {field_name}\n"
        context += f"Input Type : {field_type}\n"
        context += "Required\nHandle\n\n"

    context += "Outputs :-\n"
    for o in outputs:
        context += f"Output Name : {o.get('name', 'N/A')}\n"
        context += f"Output Type : {o.get('output_type', 'N/A')}\n\n"

    return context


def mcp_context(data):
    mcp_id = data.get("id")
    tool_name = data.get("name")
    if not mcp_id or not tool_name:
        return "MCP ID or tool name is missing."

    # url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{mcp_id}"
    # try:
    #     response = requests.get(url)
    #     response.raise_for_status()
    #     mcp_data = response.json()
    # except requests.RequestException as e:
    #     return f"Failed to fetch MCP: {e}"
    # except ValueError:
    #     return "Invalid JSON response from API."
    with open(f"post_processing/data retrival/mcps/{mcp_id}.json", "r") as f:
        mcp_data = json.load(f)
    mcp = mcp_data.get("mcp", {})
    tools = mcp.get("mcp_tools_config", {}).get("tools", [])

    # find the tool
    tool = next((t for t in tools if t.get("name") == tool_name), None)
    if not tool:
        return f"Tool '{tool_name}' not found in MCP '{mcp_id}'."

    mcp_name = mcp.get("name", "N/A")
    name = tool.get("name", "N/A")
    description = tool.get("description", "No description")
    input_schema = tool.get("input_schema", {})
    output_schema = tool.get("output_schema", {})
    try:
        original_type = "MCP_" + normalize_name(f"{mcp_name} - {name}")
    except NameError:
        original_type = f"MCP_{mcp_name}-{name}"

    context = (
        f"Name : {name}\n"
        f"Description : {description}\n"
        f"OriginalType : {original_type}\n"
        f"Type : MCP\n"
        f"MCP_id : {mcp_id}\n"
        f"ToolName : {tool_name}\n"
    )

    context += "Inputs :-\n"
    required_input = input_schema.get("required", [])
    defs = input_schema.get("$defs", {})
    properties = input_schema.get("properties", {})

    for i, property_details in properties.items():
        context += f"Input Name : {i}\n"
        context += f"Input Info : {property_details.get('description', '')}\n"

        property_type = property_details.get("type")
        options = None
        property_scheme = None
        items = None
        is_array = False

        if property_type:
            context += f"Input Type : {property_type}\n"
            if property_type == "object":
                property_scheme = property_details.get("properties", {})
            if property_type == "array":
                items = property_details.get("items")
                is_array = True
        elif "anyOf" in property_details:
            context += "Input Type : "
            for j in property_details["anyOf"]:
                if "type" in j and j["type"] != "null":
                    context += j["type"] + "\n"
                    if j["type"] == "object":
                        property_scheme = j.get("properties", {})
                    if j["type"] == "array":
                        items = j.get("items")
                        is_array = True
                elif "$ref" in j:
                    ref_key = j["$ref"].split("/")[-1]
                    ref = defs.get(ref_key, {})
                    context += ref.get("type", "N/A") + "\n"
                    if "enum" in ref:
                        options = ref["enum"]
                    if ref.get("type") == "object":
                        property_scheme = ref.get("properties", {})
                    if ref.get("type") == "array":
                        items = ref.get("items")
                        is_array = True
        elif "$ref" in property_details:
            ref_key = property_details["$ref"].split("/")[-1]
            ref = defs.get(ref_key, {})
            context += f"Input Type : {ref.get('type', 'N/A')}\n"
            if "enum" in ref:
                options = ref["enum"]
            if ref.get("type") == "object":
                property_scheme = ref.get("properties", {})
            if ref.get("type") == "array":
                items = ref.get("items")
                is_array = True

        if i in required_input:
            context += "Required\n"
        context += "Handle\n"
        if is_array:
            context += "List\n"
        if property_details.get("default") is not None:
            context += f"Default Value : {property_details['default']}\n"
        if options:
            context += "Options : " + ", ".join(options) + "\n"
        if property_scheme:
            context += "Properties :-\n"
            for j, prop in property_scheme.items():
                context += f"> Property Name : {j}\n"
                context += f"> Property Info : {prop.get('description', '')}\n"
                if prop.get("type"):
                    context += f"> Property Type : {prop['type']}\n"
                if prop.get("anyOf"):
                    context += "> Property Type : "
                    for k in prop["anyOf"]:
                        if "type" in k and k["type"] != "null":
                            context += k["type"] + ", "
                    context += "\n"
                if prop.get("default") is not None:
                    context += f"> Property Default Value : {prop['default']}\n"
                if prop.get("enum"):
                    context += "> Property Options : " + ", ".join(prop["enum"]) + "\n"
                context += "> \n"
        if items:
            context += f"Items : {items}\n"
        context += "\n"

    context += "Outputs :-\n"
    output_properties = output_schema.get("properties", {})
    for o, details in output_properties.items():
        context += f"Output Name : {o}\n"
        context += f"Output Info : {details.get('description', '')}\n"
        context += f"Output Type : {details.get('type', '')}\n\n"

    return context


context_helpers = {
    "component": context_component,
    "workflow": workflow_context,
    "mcp": mcp_context,
}
