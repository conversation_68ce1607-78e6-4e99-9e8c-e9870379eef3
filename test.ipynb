{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7d1a8528", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": 2, "id": "7799f275", "metadata": {}, "outputs": [], "source": ["url = \"http://127.0.0.1:8000/api/v1/generate_workflow\""]}, {"cell_type": "code", "execution_count": 3, "id": "93ede3d7", "metadata": {}, "outputs": [], "source": ["task = \"Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file.\""]}, {"cell_type": "code", "execution_count": 11, "id": "f6403c5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [200]>\n"]}], "source": ["payload = {\"task\": task}\n", "\n", "response = requests.post(url, json=payload)\n", "\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 10, "id": "8188ba72", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'workflow': {'nodes': [{'id': 'start-node',\n", "    'type': 'WorkflowNode',\n", "    'position': {'x': 50, 'y': 100},\n", "    'data': {'label': 'Start Node',\n", "     'type': 'component',\n", "     'originalType': 'StartNode',\n", "     'definition': {'name': 'StartNode',\n", "      'display_name': 'Start',\n", "      'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.',\n", "      'category': 'IO',\n", "      'icon': 'Play',\n", "      'beta': <PERSON><PERSON><PERSON>,\n", "      'requires_approval': <PERSON><PERSON><PERSON>,\n", "      'inputs': [],\n", "      'outputs': [{'name': 'flow',\n", "        'display_name': 'Flow',\n", "        'output_type': 'Any',\n", "        'semantic_type': None,\n", "        'method': None}],\n", "      'is_valid': True,\n", "      'path': 'components.io.startnode',\n", "      'interface_issues': []},\n", "     'config': {'collected_parameters': {'MCP_video_script_generation_video_script_generate-123456789012_topic': {'node_id': 'MCP_video_script_generation_video_script_generate-123456789012',\n", "        'input_name': 'topic',\n", "        'connected_to_start': True,\n", "        'name_node': 'Generate Video Script',\n", "        'type': 'string',\n", "        'required': True,\n", "        'options': None}}}},\n", "    'width': 200,\n", "    'height': 100,\n", "    'selected': <PERSON><PERSON><PERSON>,\n", "    'dragging': <PERSON><PERSON><PERSON>,\n", "    'style': {'opacity': 1}},\n", "   {'id': 'MCP_video_script_generation_video_script_generate-123456789012',\n", "    'type': 'WorkflowNode',\n", "    'position': {'x': 350, 'y': 100},\n", "    'data': {'label': 'Generate Video Script',\n", "     'type': 'mcp',\n", "     'originalType': 'MCP_video_script_generation_video_script_generate',\n", "     'definition': {'name': '9d749227-a133-4307-b991-d454545bccb1',\n", "      'display_name': 'video-script-generation',\n", "      'description': 'An AI-powered server that outputs structured scenes with audio narration text and matching visual descriptions, ideal for automated video content creation.',\n", "      'category': 'general',\n", "      'icon': '',\n", "      'beta': <PERSON><PERSON><PERSON>,\n", "      'inputs': [{'name': 'topic',\n", "        'display_name': 'Topic',\n", "        'info': '',\n", "        'input_type': 'string',\n", "        'input_types': ['string', 'Any'],\n", "        'required': True,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}},\n", "       {'name': 'video_time',\n", "        'display_name': 'Video time',\n", "        'info': '',\n", "        'input_type': 'integer',\n", "        'input_types': ['integer', 'Any'],\n", "        'required': True,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}},\n", "       {'name': 'scene_duration',\n", "        'display_name': 'Scene duration',\n", "        'info': '',\n", "        'input_type': 'integer',\n", "        'input_types': ['integer', 'Any'],\n", "        'required': <PERSON><PERSON><PERSON>,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}}],\n", "      'outputs': [{'name': 'result',\n", "        'display_name': 'Result',\n", "        'output_type': 'Any'}],\n", "      'is_valid': True,\n", "      'type': 'MCP',\n", "      'logo': None,\n", "      'mcp_info': {'server_id': '9d749227-a133-4307-b991-d454545bccb1',\n", "       'server_path': '',\n", "       'tool_name': 'video_script_generate',\n", "       'input_schema': {'properties': {'topic': {'title': 'Topic',\n", "          'type': 'string'},\n", "         'video_time': {'title': 'Video Time', 'type': 'integer'},\n", "         'scene_duration': {'default': 5,\n", "          'title': 'Scene Duration',\n", "          'type': 'integer'}},\n", "        'required': ['topic', 'video_time'],\n", "        'title': 'VideoScriptInput',\n", "        'type': 'object'},\n", "       'output_schema': {}}},\n", "     'config': {'video_time': 60},\n", "     'oauthConnectionState': {}},\n", "    'width': 250,\n", "    'height': 150,\n", "    'selected': True,\n", "    'positionAbsolute': {'x': 350, 'y': 100},\n", "    'dragging': <PERSON><PERSON><PERSON>,\n", "    'style': {'opacity': 1}},\n", "   {'id': 'MCP_voice_generation_mcp_generate_audio-234567890123',\n", "    'type': 'WorkflowNode',\n", "    'position': {'x': 700, 'y': 100},\n", "    'data': {'label': 'Generate Audio with Eleven Labs',\n", "     'type': 'mcp',\n", "     'originalType': 'MCP_voice_generation_mcp_generate_audio',\n", "     'definition': {'name': '068600be-4d02-4c06-a7f1-513d060cbfab',\n", "      'display_name': 'voice-generation-mcp',\n", "      'description': 'generate audio from text',\n", "      'category': 'marketing',\n", "      'icon': '',\n", "      'beta': <PERSON><PERSON><PERSON>,\n", "      'inputs': [{'name': 'script',\n", "        'display_name': '<PERSON><PERSON><PERSON>',\n", "        'info': '',\n", "        'input_type': 'string',\n", "        'input_types': ['string', 'Any'],\n", "        'required': True,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}},\n", "       {'name': 'voice_id',\n", "        'display_name': 'Voice id',\n", "        'info': '',\n", "        'input_type': 'string',\n", "        'input_types': ['string', 'Any'],\n", "        'required': True,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}},\n", "       {'name': 'provider',\n", "        'display_name': 'Provider',\n", "        'info': '',\n", "        'input_type': 'string',\n", "        'input_types': ['string', 'Any'],\n", "        'required': <PERSON><PERSON><PERSON>,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}}],\n", "      'outputs': [{'name': 'result',\n", "        'display_name': 'Result',\n", "        'output_type': 'Any'}],\n", "      'is_valid': True,\n", "      'type': 'MCP',\n", "      'logo': None,\n", "      'mcp_info': {'server_id': '068600be-4d02-4c06-a7f1-513d060cbfab',\n", "       'server_path': '',\n", "       'tool_name': 'generate_audio',\n", "       'input_schema': {'$defs': {'VoiceProvider': {'enum': ['elevenlabs',\n", "           'playht'],\n", "          'title': 'VoiceProvider',\n", "          'type': 'string'}},\n", "        'properties': {'script': {'description': '<PERSON><PERSON><PERSON> is required',\n", "          'maxLength': 10000,\n", "          'minLength': 1,\n", "          'title': '<PERSON><PERSON><PERSON>',\n", "          'type': 'string'},\n", "         'voice_id': {'maxLength': 50,\n", "          'minLength': 1,\n", "          'title': 'Voice Id',\n", "          'type': 'string'},\n", "         'provider': {'$ref': '#/$defs/VoiceProvider',\n", "          'default': 'elevenlabs',\n", "          'description': 'Optional voice provider platform'}},\n", "        'required': ['script', 'voice_id'],\n", "        'title': 'GenerateAudio',\n", "        'type': 'object'},\n", "       'output_schema': {}}},\n", "     'config': {'voice_id': 'TX3LPaxmHKxFdv7VOQHJ', 'provider': 'elevenlabs'},\n", "     'oauthConnectionState': {}},\n", "    'width': 250,\n", "    'height': 150,\n", "    'selected': True,\n", "    'positionAbsolute': {'x': 700, 'y': 100},\n", "    'dragging': <PERSON><PERSON><PERSON>,\n", "    'style': {'opacity': 1}},\n", "   {'id': 'MCP_voice_generation_mcp_fetch_audio-345678901234',\n", "    'type': 'WorkflowNode',\n", "    'position': {'x': 1050, 'y': 100},\n", "    'data': {'label': 'Fetch Audio File',\n", "     'type': 'mcp',\n", "     'originalType': 'MCP_voice_generation_mcp_fetch_audio',\n", "     'definition': {'name': '068600be-4d02-4c06-a7f1-513d060cbfab',\n", "      'display_name': 'voice-generation-mcp',\n", "      'description': 'generate audio from text',\n", "      'category': 'marketing',\n", "      'icon': '',\n", "      'beta': <PERSON><PERSON><PERSON>,\n", "      'inputs': [{'name': 'audio_ids',\n", "        'display_name': 'Audio ids',\n", "        'info': '',\n", "        'input_type': 'array',\n", "        'input_types': ['array', 'Any'],\n", "        'required': True,\n", "        'is_handle': True,\n", "        'is_list': True,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}},\n", "       {'name': 'provider',\n", "        'display_name': 'Provider',\n", "        'info': '',\n", "        'input_type': 'string',\n", "        'input_types': ['string', 'Any'],\n", "        'required': <PERSON><PERSON><PERSON>,\n", "        'is_handle': True,\n", "        'is_list': <PERSON><PERSON><PERSON>,\n", "        'real_time_refresh': <PERSON><PERSON><PERSON>,\n", "        'advanced': <PERSON><PERSON><PERSON>,\n", "        'value': None,\n", "        'options': None,\n", "        'visibility_rules': None,\n", "        'visibility_logic': 'OR',\n", "        'validation': {}}],\n", "      'outputs': [{'name': 'result',\n", "        'display_name': 'Result',\n", "        'output_type': 'Any'}],\n", "      'is_valid': True,\n", "      'type': 'MCP',\n", "      'logo': None,\n", "      'mcp_info': {'server_id': '068600be-4d02-4c06-a7f1-513d060cbfab',\n", "       'server_path': '',\n", "       'tool_name': 'fetch_audio',\n", "       'input_schema': {'$defs': {'VoiceProvider': {'enum': ['elevenlabs',\n", "           'playht'],\n", "          'title': 'VoiceProvider',\n", "          'type': 'string'}},\n", "        'properties': {'audio_ids': {'description': 'List of voice IDs is required',\n", "          'items': {'type': 'string'},\n", "          'minItems': 1,\n", "          'title': 'Audio Ids',\n", "          'type': 'array'},\n", "         'provider': {'$ref': '#/$defs/VoiceProvider',\n", "          'default': 'elevenlabs',\n", "          'description': 'Optional voice provider platform'}},\n", "        'required': ['audio_ids'],\n", "        'title': 'FetchGenerateAudio',\n", "        'type': 'object'},\n", "       'output_schema': {}}},\n", "     'config': {'provider': 'elevenlabs'},\n", "     'oauthConnectionState': {}},\n", "    'width': 250,\n", "    'height': 150,\n", "    'selected': True,\n", "    'positionAbsolute': {'x': 1050, 'y': 100},\n", "    'dragging': <PERSON><PERSON><PERSON>,\n", "    'style': {'opacity': 1}}],\n", "  'edges': [{'animated': True,\n", "    'style': {'strokeWidth': 2, 'zIndex': 5},\n", "    'id': 'reactflow__edgestart-nodeflow-MCP_video_script_generation_video_script_generate-123456789012topic',\n", "    'source': 'start-node',\n", "    'sourceHandle': 'flow',\n", "    'target': 'MCP_video_script_generation_video_script_generate-123456789012',\n", "    'targetHandle': 'topic',\n", "    'type': 'default',\n", "    'selected': <PERSON>als<PERSON>},\n", "   {'animated': True,\n", "    'style': {'strokeWidth': 2, 'zIndex': 5},\n", "    'id': 'reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012audio_text-MCP_voice_generation_mcp_generate_audio-234567890123script',\n", "    'source': 'MCP_video_script_generation_video_script_generate-123456789012',\n", "    'sourceHandle': 'audio_text',\n", "    'target': 'MCP_voice_generation_mcp_generate_audio-234567890123',\n", "    'targetHandle': 'script',\n", "    'type': 'default',\n", "    'selected': <PERSON>als<PERSON>},\n", "   {'animated': True,\n", "    'style': {'strokeWidth': 2, 'zIndex': 5},\n", "    'id': 'reactflow__edgeMCP_voice_generation_mcp_generate_audio-234567890123audio_ids-MCP_voice_generation_mcp_fetch_audio-345678901234audio_ids',\n", "    'source': 'MCP_voice_generation_mcp_generate_audio-234567890123',\n", "    'sourceHandle': 'audio_ids',\n", "    'target': 'MCP_voice_generation_mcp_fetch_audio-345678901234',\n", "    'targetHandle': 'audio_ids',\n", "    'type': 'default',\n", "    'selected': False}]},\n", " 'session_id': 'c18658bd-afe8-46ed-a841-80c29a38bc6c'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": null, "id": "2b4e72ad", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}