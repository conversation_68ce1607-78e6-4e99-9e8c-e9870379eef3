<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Generation SSE Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-section {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .events-section {
            margin-top: 20px;
        }
        .event {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .event.start { border-left-color: #28a745; }
        .event.processing { border-left-color: #ffc107; }
        .event.agent_complete { border-left-color: #17a2b8; }
        .event.post_processing { border-left-color: #6f42c1; }
        .event.success { border-left-color: #28a745; }
        .event.complete { border-left-color: #28a745; }
        .event.error { border-left-color: #dc3545; background: #f8d7da; }
        .event.done { border-left-color: #6c757d; }
        .event-type {
            font-weight: bold;
            color: #495057;
        }
        .event-data {
            margin-top: 5px;
            font-family: monospace;
            font-size: 12px;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .clear-btn {
            background: #6c757d;
            margin-left: 10px;
        }
        .clear-btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Workflow Generation SSE Test Client</h1>
        
        <div class="input-section">
            <label for="task">Task Description:</label>
            <textarea id="task" placeholder="Enter your workflow task here...">Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file.</textarea>
            <br>
            <button id="startBtn" onclick="startStreaming()">Start Streaming</button>
            <button id="stopBtn" onclick="stopStreaming()" disabled>Stop Streaming</button>
            <button class="clear-btn" onclick="clearEvents()">Clear Events</button>
        </div>

        <div class="status" id="status">Disconnected</div>

        <div class="events-section">
            <h3>Events Stream:</h3>
            <div id="events"></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let eventCount = 0;

        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function addEvent(type, data) {
            eventCount++;
            const eventsDiv = document.getElementById('events');
            const eventDiv = document.createElement('div');
            eventDiv.className = `event ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            eventDiv.innerHTML = `
                <div class="event-type">#${eventCount} [${timestamp}] ${type.toUpperCase()}</div>
                <div class="event-data">${JSON.stringify(data, null, 2)}</div>
            `;
            
            eventsDiv.appendChild(eventDiv);
            eventsDiv.scrollTop = eventsDiv.scrollHeight;
        }

        function startStreaming() {
            const task = document.getElementById('task').value.trim();
            if (!task) {
                alert('Please enter a task description');
                return;
            }

            // Close existing connection
            if (eventSource) {
                eventSource.close();
            }

            updateStatus('Connecting...', 'connecting');
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;

            // Create new EventSource connection
            // Note: EventSource doesn't support POST directly, so we'll use fetch with streaming
            startFetchStream(task);
        }

        async function startFetchStream(task) {
            try {
                const response = await fetch('/api/v1/generate_workflow/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task: task })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                updateStatus('Connected', 'connected');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    let eventType = null;
                    let eventData = null;

                    for (const line of lines) {
                        if (line.startsWith('event: ')) {
                            eventType = line.substring(7).trim();
                        } else if (line.startsWith('data: ')) {
                            eventData = line.substring(6).trim();
                        } else if (line.trim() === '' && eventType && eventData) {
                            // Complete event received
                            try {
                                const data = JSON.parse(eventData);
                                addEvent(eventType, data);
                                
                                // Auto-stop on done event
                                if (eventType === 'done' || eventType === 'error') {
                                    setTimeout(stopStreaming, 1000);
                                }
                            } catch (e) {
                                console.error('Failed to parse event data:', e);
                            }
                            eventType = null;
                            eventData = null;
                        }
                    }
                }
            } catch (error) {
                console.error('Streaming error:', error);
                addEvent('error', { message: error.message });
                updateStatus('Error: ' + error.message, 'disconnected');
                stopStreaming();
            }
        }

        function stopStreaming() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            updateStatus('Disconnected', 'disconnected');
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        function clearEvents() {
            document.getElementById('events').innerHTML = '';
            eventCount = 0;
        }

        // Handle page unload
        window.addEventListener('beforeunload', stopStreaming);
    </script>
</body>
</html>
