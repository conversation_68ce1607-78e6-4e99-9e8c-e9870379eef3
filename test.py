import requests

url = "http://127.0.0.1:8000/api/v1/generate_workflow"
task = "Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file."
payload = {"task": task}

response = requests.post(url, json=payload)

print(response.content)
