import asyncio
import json
import uuid
from typing import Any, As<PERSON><PERSON><PERSON>ator, Dict

from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from .agent import get_agents
from .graph import get_graph
from .logger_config import get_logger


class Payload(BaseModel):
    task: str


app = FastAPI()
logger = get_logger(str(uuid.uuid4()))
agents = get_agents(logger)
graph = get_graph(agents)


def get_workflow(task: str) -> Dict[str, Any]:
    result = graph(task)
    result = result.results
    response: Dict[str, Any] = {}

    if "post_processing" in result:
        post_result = result["post_processing"].result.message["content"][0]["text"]

        if "```json" in post_result:
            post_result = post_result.split("```json")[1].split("```")[0].strip()

        try:
            parsed_result = json.loads(post_result)
        except json.JSONDecodeError:
            return {
                "error": "Failed to parse JSON from post_processing result",
                "raw_result": post_result,
            }

        response["workflow"] = parsed_result
    else:
        router_result = result["router"].result.message["content"][0]["text"]
        response["message"] = router_result

    return response


async def get_workflow_stream(task: str) -> AsyncGenerator[str, None]:
    """
    Stream workflow generation process with Server-Sent Events.
    Yields SSE-formatted messages for each step of the workflow generation.
    """

    def format_sse_message(event_type: str, data: Dict[str, Any]) -> str:
        """Format data as Server-Sent Events message"""
        return f"event: {event_type}\ndata: {json.dumps(data)}\n\n"

    try:
        # Send initial event
        yield format_sse_message(
            "start",
            {
                "message": "Starting workflow generation",
                "task": task,
                "timestamp": asyncio.get_event_loop().time(),
            },
        )

        # Log user input
        logger.info(
            json.dumps(
                {
                    "message": {"role": "USER_INPUT", "content": task},
                    "agent": "prompt_enhancement",
                }
            )
        )

        # Send processing event
        yield format_sse_message(
            "processing",
            {
                "message": "Processing task through workflow agents",
                "stage": "graph_execution",
                "timestamp": asyncio.get_event_loop().time(),
            },
        )

        # Execute the graph in a thread to avoid blocking
        result = await asyncio.to_thread(graph, task)
        result = result.results

        # Send intermediate results for each agent
        for agent_name in result.keys():
            yield format_sse_message(
                "agent_complete",
                {
                    "agent": agent_name,
                    "message": f"Agent {agent_name} completed processing",
                    "timestamp": asyncio.get_event_loop().time(),
                },
            )

        # Process final results
        response: Dict[str, Any] = {}

        if "post_processing" in result:
            yield format_sse_message(
                "post_processing",
                {
                    "message": "Processing final workflow output",
                    "timestamp": asyncio.get_event_loop().time(),
                },
            )

            post_result = result["post_processing"].result.message["content"][0]["text"]

            if "```json" in post_result:
                post_result = post_result.split("```json")[1].split("```")[0].strip()

            try:
                parsed_result = json.loads(post_result)
                response["workflow"] = parsed_result

                yield format_sse_message(
                    "success",
                    {
                        "message": "Workflow generated successfully",
                        "workflow": parsed_result,
                        "timestamp": asyncio.get_event_loop().time(),
                    },
                )

            except json.JSONDecodeError as e:
                error_response = {
                    "error": "Failed to parse JSON from post_processing result",
                    "raw_result": post_result,
                    "timestamp": asyncio.get_event_loop().time(),
                }
                response.update(error_response)

                yield format_sse_message("error", error_response)
        else:
            router_result = result["router"].result.message["content"][0]["text"]
            response["message"] = router_result

            yield format_sse_message(
                "complete",
                {
                    "message": router_result,
                    "timestamp": asyncio.get_event_loop().time(),
                },
            )

        # Send final completion event
        yield format_sse_message(
            "done",
            {
                "message": "Workflow generation completed",
                "result": response,
                "timestamp": asyncio.get_event_loop().time(),
            },
        )

    except Exception as e:
        # Send error event
        yield format_sse_message(
            "error",
            {
                "error": str(e),
                "message": "An error occurred during workflow generation",
                "timestamp": asyncio.get_event_loop().time(),
            },
        )


@app.post("/api/v1/generate_workflow")
async def generate_workflow(payload: Payload):
    task = payload.task

    logger.info(
        json.dumps(
            {
                "message": {"role": "USER_INPUT", "content": task},
                "agent": "prompt_enhancement",
            }
        )
    )

    response = get_workflow(task)

    # result = await asyncio.to_thread(graph, task)
    return response


@app.post("/api/v1/generate_workflow/stream")
async def generate_workflow_stream(payload: Payload):
    """
    Server-Sent Events endpoint for streaming workflow generation.

    Returns a stream of events showing the progress of workflow generation:
    - start: Initial event when processing begins
    - processing: When the graph execution starts
    - agent_complete: When each agent completes processing
    - post_processing: When final processing begins
    - success: When workflow is successfully generated
    - complete: When processing completes with a message
    - error: If an error occurs
    - done: Final event indicating completion

    Usage:
    ```javascript
    const eventSource = new EventSource('/api/v1/generate_workflow/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ task: 'your task here' })
    });

    eventSource.addEventListener('start', (event) => {
        console.log('Started:', JSON.parse(event.data));
    });

    eventSource.addEventListener('success', (event) => {
        console.log('Workflow:', JSON.parse(event.data));
    });

    eventSource.addEventListener('done', (event) => {
        console.log('Completed:', JSON.parse(event.data));
        eventSource.close();
    });
    ```
    """
    task = payload.task

    return StreamingResponse(
        get_workflow_stream(task),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )
