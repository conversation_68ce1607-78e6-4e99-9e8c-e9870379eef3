#!/usr/bin/env python3
"""
Quick test script to verify the SSE endpoint is working correctly.
"""

import asyncio
import json
from api import get_workflow_stream


async def test_sse_function():
    """Test the SSE streaming function directly."""
    print("🧪 Testing SSE streaming function...")
    
    task = "Create a simple workflow for testing"
    event_count = 0
    
    try:
        async for event in get_workflow_stream(task):
            event_count += 1
            print(f"📨 Event #{event_count}:")
            
            # Parse the SSE format
            lines = event.strip().split('\n')
            event_type = None
            event_data = None
            
            for line in lines:
                if line.startswith('event: '):
                    event_type = line[7:].strip()
                elif line.startswith('data: '):
                    event_data = line[6:].strip()
            
            if event_type and event_data:
                try:
                    data = json.loads(event_data)
                    print(f"   Type: {event_type}")
                    print(f"   Message: {data.get('message', 'N/A')}")
                    
                    if event_type == 'done':
                        print("✅ Stream completed successfully!")
                        break
                    elif event_type == 'error':
                        print(f"❌ Error occurred: {data.get('error', 'Unknown error')}")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON: {e}")
                    print(f"   Raw data: {event_data}")
            
            print()  # Empty line for readability
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    print(f"📊 Total events received: {event_count}")
    return event_count > 0


if __name__ == "__main__":
    print("🚀 Starting SSE function test...")
    success = asyncio.run(test_sse_function())
    
    if success:
        print("\n✅ SSE function test passed!")
        print("\n📋 Next steps:")
        print("1. Start the FastAPI server: uvicorn api:app --reload")
        print("2. Test with Python client: python sse_test_client.py")
        print("3. Test with HTML client: Open sse_test_client.html in browser")
    else:
        print("\n❌ SSE function test failed!")
